import { ConversationMemory } from "@/lib/ai/memory/conversation-memory";
import { handleCorsPreflightRequest } from "@/lib/middleware/cors";
import type { NextRequest } from "next/server";

// Initialize conversation memory
const conversationMemory = new ConversationMemory();

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

/**
 * Get conversation history for a visitor on a specific website
 * This endpoint is used by the chat widget to load previous conversations
 */
export async function GET(
	_req: NextRequest,
	{
		params,
	}: {
		params: Promise<{ websiteId: string; visitorId: string }>;
	},
) {
	try {
		const { websiteId, visitorId } = await params;

		console.log(
			`[Chat History API] Retrieving conversation history for websiteId: ${websiteId}, visitorId: ${visitorId}`,
		);

		// Validate required parameters
		if (!websiteId || !visitorId) {
			return new Response(
				JSON.stringify({ error: "websiteId and visitorId are required" }),
				{
					status: 400,
					headers: {
						"Content-Type": "application/json",
						"Access-Control-Allow-Origin": "*",
						"Access-Control-Allow-Methods": "GET, OPTIONS",
						"Access-Control-Allow-Headers": "Content-Type",
					},
				},
			);
		}

		// Get the most recent active conversation for this visitor
		const conversation = await conversationMemory.getVisitorConversation({
			websiteId,
			visitorId,
		});

		if (!conversation) {
			console.log(
				`[Chat History API] No conversation found for visitor ${visitorId} on website ${websiteId}`,
			);
			return new Response(
				JSON.stringify({
					conversation: null,
					messages: [],
				}),
				{
					headers: {
						"Content-Type": "application/json",
						"Access-Control-Allow-Origin": "*",
						"Access-Control-Allow-Methods": "GET, OPTIONS",
						"Access-Control-Allow-Headers": "Content-Type",
					},
				},
			);
		}

		console.log(
			`[Chat History API] Found conversation ${conversation.id}, retrieving messages`,
		);

		// Get all messages for the conversation
		const messages = await conversationMemory.getMessages(conversation.id);

		console.log(
			`[Chat History API] Retrieved ${messages.length} messages for conversation ${conversation.id}`,
		);

		// Return the conversation and messages
		return new Response(
			JSON.stringify({
				conversation,
				messages: messages.map((message) => ({
					id: message.id,
					content: message.content,
					role: message.role,
					timestamp: message.createdAt,
				})),
			}),
			{
				headers: {
					"Content-Type": "application/json",
					"Access-Control-Allow-Origin": "*",
					"Access-Control-Allow-Methods": "GET, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type",
				},
			},
		);
	} catch (error) {
		console.error(
			"[Chat History API] Error retrieving conversation history:",
			error,
		);
		return new Response(
			JSON.stringify({ error: "Failed to retrieve conversation history" }),
			{
				status: 500,
				headers: {
					"Content-Type": "application/json",
					"Access-Control-Allow-Origin": "*",
					"Access-Control-Allow-Methods": "GET, OPTIONS",
					"Access-Control-Allow-Headers": "Content-Type",
				},
			},
		);
	}
}
