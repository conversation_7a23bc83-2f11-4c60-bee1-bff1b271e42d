/**
 * Utility function to clean streaming content from AI responses
 * Removes formatting artifacts, extra quotes, and JSON stringification issues
 */
export function cleanStreamingContent(content: string): string {
	if (!content || typeof content !== "string") {
		return "";
	}

	return (
		content
			// First, convert literal \n strings to actual newlines
			.replace(/\\n/g, "\n") // Convert literal \n to actual newlines
			.replace(/\\r\\n/g, "\n") // Convert literal \r\n to actual newlines
			.replace(/\\r/g, "\n") // Convert literal \r to actual newlines

			// Handle the specific pattern: "Hello""! I""'""m"" here to assist"" you."
			.replace(/([a-zA-Z])""/g, "$1") // Remove double quotes after letters
			.replace(/""([a-zA-Z])/g, "$1") // Remove double quotes before letters
			.replace(/""([!?.,;:])/g, "$1") // Remove double quotes before punctuation
			.replace(/([!?.,;:])""/g, "$1") // Remove double quotes after punctuation
			.replace(/""'/g, "'") // Fix quote-apostrophe patterns
			.replace(/'""/g, "'") // Fix apostrophe-quote patterns

			// Remove various quote patterns
			.replace(/"""/g, '"') // Triple quotes to single
			.replace(/""/g, "") // Remove double quotes entirely
			.replace(/\\"/g, '"') // Escaped quotes
			.replace(/\\""/g, '"') // Escaped double quotes
			.replace(/"{2,}/g, '"') // Multiple consecutive quotes

			// Handle JSON-like quote patterns
			.replace(/"([^"]*)""/g, '"$1"') // Fix quote-quote patterns
			.replace(/""([^"]*)""/g, '"$1"') // Fix double-quote wrapping
			.replace(/\\\\"/g, '"') // Double-escaped quotes

			// Handle specific patterns that might come from JSON stringification
			.replace(/"\s*"/g, '"') // Quotes with spaces

			// Clean up any remaining quote artifacts
			.replace(/([a-zA-Z])"([a-zA-Z])/g, "$1'$2") // Replace quotes between letters with apostrophes
			.replace(/\s+"/g, ' "') // Fix spacing before quotes
			.replace(/"\s+/g, '" ') // Fix spacing after quotes
			.trim()

			// Remove surrounding quotes from the entire message (after all other cleaning)
			// Handle multiple quotes at start/end and both single and double quotes
			.replace(/^["']+(.*)["']+$/, "$1") // Remove surrounding quotes (single or double, multiple)
			.replace(/^["']\s*([\s\S]*?)\s*["']$/, "$1") // Remove quotes with optional whitespace (multiline safe)

			// Handle cases where quotes might be at start or end only
			.replace(/^["']+\s*/, "") // Remove quotes at start with optional whitespace
			.replace(/\s*["']+$/, "") // Remove quotes at end with optional whitespace
			.trim()
	);
}

/**
 * Debug function to log content cleaning process
 * Useful for troubleshooting content formatting issues
 */
export function debugContentCleaning(
	originalContent: string,
	cleanedContent: string,
	context = "",
): void {
	if (originalContent !== cleanedContent) {
		console.log(`[Content Cleaner] ${context} - Content was cleaned:`);
		console.log(`[Content Cleaner] Original: "${originalContent}"`);
		console.log(`[Content Cleaner] Cleaned:  "${cleanedContent}"`);
	}
}
