import { db } from "@/lib/db";
import { conversationsTable, messagesTable } from "@/lib/db/schema";
import type {
	Conversation,
	NewConversation,
} from "@/lib/db/schema/conversations";
import type { Message, NewMessage } from "@/lib/db/schema/messages";
import { and, desc, eq } from "drizzle-orm";

// UUID validation regex
const UUID_REGEX =
	/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

/**
 * Conversation memory service for storing and retrieving conversation history
 */
export class ConversationMemory {
	/**
	 * Create a new conversation
	 */
	async createConversation(data: {
		websiteId: string;
		visitorId: string;
		id?: string; // Optional ID parameter
	}): Promise<Conversation> {
		console.log(
			`[ConversationMemory] Creating new conversation for website ${
				data.websiteId
			} and visitor ${data.visitorId}${data.id ? ` with ID ${data.id}` : ""}`,
		);

		// Validate the ID if provided
		if (data.id && !UUID_REGEX.test(data.id)) {
			console.error(
				`[ConversationMemory] Invalid conversation ID format: ${data.id}. Must be a valid UUID.`,
			);
			throw new Error(
				`Invalid conversation ID format: ${data.id}. Must be a valid UUID.`,
			);
		}

		try {
			const newConversation: NewConversation = {
				...(data.id && { id: data.id }), // Include ID if provided
				websiteId: data.websiteId,
				visitorId: data.visitorId,
				status: "active",
			};

			const [conversation] = await db
				.insert(conversationsTable)
				.values(newConversation)
				.returning();

			console.log(
				`[ConversationMemory] Created conversation with ID: ${conversation.id}`,
			);
			return conversation as Conversation;
		} catch (error) {
			console.error("[ConversationMemory] Error creating conversation:", error);
			throw error;
		}
	}

	/**
	 * Get a conversation by ID
	 */
	async getConversation(conversationId: string): Promise<Conversation | null> {
		console.log(
			`[ConversationMemory] Getting conversation with ID: ${conversationId}`,
		);

		// Validate that the conversation ID is a valid UUID
		if (!UUID_REGEX.test(conversationId)) {
			console.error(
				`[ConversationMemory] Invalid conversation ID format: ${conversationId}. Must be a valid UUID.`,
			);
			return null;
		}

		try {
			const [conversation] = await db
				.select()
				.from(conversationsTable)
				.where(eq(conversationsTable.id, conversationId));

			if (conversation) {
				console.log(
					`[ConversationMemory] Found conversation for website: ${conversation.websiteId}`,
				);
			} else {
				console.log(
					`[ConversationMemory] No conversation found with ID: ${conversationId}`,
				);
			}

			return conversation ? (conversation as Conversation) : null;
		} catch (error) {
			console.error("[ConversationMemory] Error getting conversation:", error);
			throw error;
		}
	}

	/**
	 * Add a message to a conversation
	 */
	async addMessage(data: {
		conversationId: string;
		content: string;
		role: "user" | "assistant";
	}): Promise<Message> {
		console.log(
			`[ConversationMemory] Adding ${data.role} message to conversation: ${data.conversationId}`,
		);

		// Validate that the conversation ID is a valid UUID
		if (!UUID_REGEX.test(data.conversationId)) {
			console.error(
				`[ConversationMemory] Invalid conversation ID format: ${data.conversationId}. Must be a valid UUID.`,
			);
			throw new Error(
				`Invalid conversation ID format: ${data.conversationId}. Must be a valid UUID.`,
			);
		}

		try {
			const newMessage: NewMessage = {
				conversationId: data.conversationId,
				content: data.content,
				role: data.role,
			};

			const [message] = await db
				.insert(messagesTable)
				.values(newMessage)
				.returning();

			console.log(`[ConversationMemory] Added message with ID: ${message.id}`);
			return message as Message;
		} catch (error) {
			console.error("[ConversationMemory] Error adding message:", error);
			throw error;
		}
	}

	/**
	 * Get all messages for a conversation
	 */
	async getMessages(conversationId: string): Promise<Message[]> {
		console.log(
			`[ConversationMemory] Getting messages for conversation: ${conversationId}`,
		);

		// Validate that the conversation ID is a valid UUID
		if (!UUID_REGEX.test(conversationId)) {
			console.error(
				`[ConversationMemory] Invalid conversation ID format: ${conversationId}. Must be a valid UUID.`,
			);
			return []; // Return empty array for invalid IDs
		}

		try {
			const messages = await db
				.select()
				.from(messagesTable)
				.where(eq(messagesTable.conversationId, conversationId))
				.orderBy(messagesTable.createdAt);

			console.log(
				`[ConversationMemory] Found ${messages.length} messages for conversation: ${conversationId}`,
			);
			return messages as Message[];
		} catch (error) {
			console.error("[ConversationMemory] Error getting messages:", error);
			throw error;
		}
	}

	/**
	 * Get or create a conversation for a website visitor
	 */
	async getOrCreateConversation(data: {
		websiteId: string;
		visitorId: string;
	}): Promise<Conversation> {
		console.log(
			`[ConversationMemory] Getting or creating conversation for website: ${data.websiteId}, visitor: ${data.visitorId}`,
		);

		try {
			// Try to find an active conversation for this visitor and website
			console.log(
				`[ConversationMemory] Looking for active conversation with websiteId=${data.websiteId}, visitorId=${data.visitorId}`,
			);

			const existingConversations = await db
				.select()
				.from(conversationsTable)
				.where(
					and(
						eq(conversationsTable.websiteId, data.websiteId),
						eq(conversationsTable.visitorId, data.visitorId),
						eq(conversationsTable.status, "active"),
					),
				);

			console.log(
				`[ConversationMemory] Found ${existingConversations.length} active conversations for this visitor and website`,
			);

			if (existingConversations.length > 0) {
				const existingConversation = existingConversations[0];
				console.log(
					`[ConversationMemory] Found existing conversation: ${existingConversation.id} for website ${data.websiteId} and visitor ${data.visitorId}`,
				);
				return existingConversation as Conversation;
			}

			console.log(
				`[ConversationMemory] No existing conversation found for website ${data.websiteId} and visitor ${data.visitorId}, creating new one`,
			);
			// Create a new conversation if none exists
			return this.createConversation(data);
		} catch (error) {
			console.error(
				"[ConversationMemory] Error in getOrCreateConversation:",
				error,
			);
			throw error;
		}
	}

	/**
	 * Get the most recent active conversation for a visitor on a website
	 */
	async getVisitorConversation(data: {
		websiteId: string;
		visitorId: string;
	}): Promise<Conversation | null> {
		console.log(
			`[ConversationMemory] Getting most recent conversation for website: ${data.websiteId}, visitor: ${data.visitorId}`,
		);

		try {
			const conversations = await db
				.select()
				.from(conversationsTable)
				.where(
					and(
						eq(conversationsTable.websiteId, data.websiteId),
						eq(conversationsTable.visitorId, data.visitorId),
						eq(conversationsTable.status, "active"),
					),
				)
				.orderBy(desc(conversationsTable.updatedAt))
				.limit(1);

			if (conversations.length > 0) {
				console.log(
					`[ConversationMemory] Found most recent conversation: ${conversations[0].id}`,
				);
				return conversations[0] as Conversation;
			}

			console.log(
				`[ConversationMemory] No active conversation found for visitor ${data.visitorId} on website ${data.websiteId}`,
			);
			return null;
		} catch (error) {
			console.error(
				"[ConversationMemory] Error getting visitor conversation:",
				error,
			);
			throw error;
		}
	}
}
