
/**
 * Bubl Widget V2 Bundle
 * Self-contained React widget with Shadow DOM isolation
 * Generated: 2025-06-03T09:52:41.546Z
 */

"use strict";(()=>{var xx=Object.create;var za=Object.defineProperty,vx=Object.defineProperties,Sx=Object.getOwnPropertyDescriptor,kx=Object.getOwnPropertyDescriptors,wx=Object.getOwnPropertyNames,Aa=Object.getOwnPropertySymbols,Ex=Object.getPrototypeOf,Yu=Object.prototype.hasOwnProperty,dp=Object.prototype.propertyIsEnumerable;var gp=Math.pow,hp=(t,e,n)=>e in t?za(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,C=(t,e)=>{for(var n in e||(e={}))Yu.call(e,n)&&hp(t,n,e[n]);if(Aa)for(var n of Aa(e))dp.call(e,n)&&hp(t,n,e[n]);return t},Et=(t,e)=>vx(t,kx(e));var Ca=(t,e)=>{var n={};for(var l in t)Yu.call(t,l)&&e.indexOf(l)<0&&(n[l]=t[l]);if(t!=null&&Aa)for(var l of Aa(t))e.indexOf(l)<0&&dp.call(t,l)&&(n[l]=t[l]);return n};var ie=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),yp=(t,e)=>{for(var n in e)za(t,n,{get:e[n],enumerable:!0})},Tx=(t,e,n,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of wx(e))!Yu.call(t,i)&&i!==n&&za(t,i,{get:()=>e[i],enumerable:!(l=Sx(e,i))||l.enumerable});return t};var Ie=(t,e,n)=>(n=t!=null?xx(Ex(t)):{},Tx(e||!t||!t.__esModule?za(n,"default",{value:t,enumerable:!0}):n,t));var Il=(t,e,n)=>new Promise((l,i)=>{var r=u=>{try{o(n.next(u))}catch(c){i(c)}},a=u=>{try{o(n.throw(u))}catch(c){i(c)}},o=u=>u.done?l(u.value):Promise.resolve(u.value).then(r,a);o((n=n.apply(t,e)).next())});var Mp=ie(K=>{"use strict";var Xu=Symbol.for("react.transitional.element"),Ax=Symbol.for("react.portal"),zx=Symbol.for("react.fragment"),Cx=Symbol.for("react.strict_mode"),Mx=Symbol.for("react.profiler"),Dx=Symbol.for("react.consumer"),Ox=Symbol.for("react.context"),Rx=Symbol.for("react.forward_ref"),_x=Symbol.for("react.suspense"),Nx=Symbol.for("react.memo"),wp=Symbol.for("react.lazy"),bp=Symbol.iterator;function Lx(t){return t===null||typeof t!="object"?null:(t=bp&&t[bp]||t["@@iterator"],typeof t=="function"?t:null)}var Ep={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Tp=Object.assign,Ap={};function Kl(t,e,n){this.props=t,this.context=e,this.refs=Ap,this.updater=n||Ep}Kl.prototype.isReactComponent={};Kl.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Kl.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function zp(){}zp.prototype=Kl.prototype;function Qu(t,e,n){this.props=t,this.context=e,this.refs=Ap,this.updater=n||Ep}var Zu=Qu.prototype=new zp;Zu.constructor=Qu;Tp(Zu,Kl.prototype);Zu.isPureReactComponent=!0;var xp=Array.isArray,xt={H:null,A:null,T:null,S:null,V:null},Cp=Object.prototype.hasOwnProperty;function Iu(t,e,n,l,i,r){return n=r.ref,{$$typeof:Xu,type:t,key:e,ref:n!==void 0?n:null,props:r}}function Ux(t,e){return Iu(t.type,e,void 0,void 0,void 0,t.props)}function Fu(t){return typeof t=="object"&&t!==null&&t.$$typeof===Xu}function Bx(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var vp=/\/+/g;function Vu(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Bx(""+t.key):e.toString(36)}function Sp(){}function Hx(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(Sp,Sp):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function Fl(t,e,n,l,i){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(r){case"bigint":case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case Xu:case Ax:a=!0;break;case wp:return a=t._init,Fl(a(t._payload),e,n,l,i)}}if(a)return i=i(t),a=l===""?"."+Vu(t,0):l,xp(i)?(n="",a!=null&&(n=a.replace(vp,"$&/")+"/"),Fl(i,e,n,"",function(c){return c})):i!=null&&(Fu(i)&&(i=Ux(i,n+(i.key==null||t&&t.key===i.key?"":(""+i.key).replace(vp,"$&/")+"/")+a)),e.push(i)),1;a=0;var o=l===""?".":l+":";if(xp(t))for(var u=0;u<t.length;u++)l=t[u],r=o+Vu(l,u),a+=Fl(l,e,n,r,i);else if(u=Lx(t),typeof u=="function")for(t=u.call(t),u=0;!(l=t.next()).done;)l=l.value,r=o+Vu(l,u++),a+=Fl(l,e,n,r,i);else if(r==="object"){if(typeof t.then=="function")return Fl(Hx(t),e,n,l,i);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return a}function Ma(t,e,n){if(t==null)return t;var l=[],i=0;return Fl(t,l,"","",function(r){return e.call(n,r,i++)}),l}function qx(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var kp=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function jx(){}K.Children={map:Ma,forEach:function(t,e,n){Ma(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return Ma(t,function(){e++}),e},toArray:function(t){return Ma(t,function(e){return e})||[]},only:function(t){if(!Fu(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};K.Component=Kl;K.Fragment=zx;K.Profiler=Mx;K.PureComponent=Qu;K.StrictMode=Cx;K.Suspense=_x;K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=xt;K.__COMPILER_RUNTIME={__proto__:null,c:function(t){return xt.H.useMemoCache(t)}};K.cache=function(t){return function(){return t.apply(null,arguments)}};K.cloneElement=function(t,e,n){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var l=Tp({},t.props),i=t.key,r=void 0;if(e!=null)for(a in e.ref!==void 0&&(r=void 0),e.key!==void 0&&(i=""+e.key),e)!Cp.call(e,a)||a==="key"||a==="__self"||a==="__source"||a==="ref"&&e.ref===void 0||(l[a]=e[a]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var o=Array(a),u=0;u<a;u++)o[u]=arguments[u+2];l.children=o}return Iu(t.type,i,void 0,void 0,r,l)};K.createContext=function(t){return t={$$typeof:Ox,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:Dx,_context:t},t};K.createElement=function(t,e,n){var l,i={},r=null;if(e!=null)for(l in e.key!==void 0&&(r=""+e.key),e)Cp.call(e,l)&&l!=="key"&&l!=="__self"&&l!=="__source"&&(i[l]=e[l]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var o=Array(a),u=0;u<a;u++)o[u]=arguments[u+2];i.children=o}if(t&&t.defaultProps)for(l in a=t.defaultProps,a)i[l]===void 0&&(i[l]=a[l]);return Iu(t,r,void 0,void 0,null,i)};K.createRef=function(){return{current:null}};K.forwardRef=function(t){return{$$typeof:Rx,render:t}};K.isValidElement=Fu;K.lazy=function(t){return{$$typeof:wp,_payload:{_status:-1,_result:t},_init:qx}};K.memo=function(t,e){return{$$typeof:Nx,type:t,compare:e===void 0?null:e}};K.startTransition=function(t){var e=xt.T,n={};xt.T=n;try{var l=t(),i=xt.S;i!==null&&i(n,l),typeof l=="object"&&l!==null&&typeof l.then=="function"&&l.then(jx,kp)}catch(r){kp(r)}finally{xt.T=e}};K.unstable_useCacheRefresh=function(){return xt.H.useCacheRefresh()};K.use=function(t){return xt.H.use(t)};K.useActionState=function(t,e,n){return xt.H.useActionState(t,e,n)};K.useCallback=function(t,e){return xt.H.useCallback(t,e)};K.useContext=function(t){return xt.H.useContext(t)};K.useDebugValue=function(){};K.useDeferredValue=function(t,e){return xt.H.useDeferredValue(t,e)};K.useEffect=function(t,e,n){var l=xt.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return l.useEffect(t,e)};K.useId=function(){return xt.H.useId()};K.useImperativeHandle=function(t,e,n){return xt.H.useImperativeHandle(t,e,n)};K.useInsertionEffect=function(t,e){return xt.H.useInsertionEffect(t,e)};K.useLayoutEffect=function(t,e){return xt.H.useLayoutEffect(t,e)};K.useMemo=function(t,e){return xt.H.useMemo(t,e)};K.useOptimistic=function(t,e){return xt.H.useOptimistic(t,e)};K.useReducer=function(t,e,n){return xt.H.useReducer(t,e,n)};K.useRef=function(t){return xt.H.useRef(t)};K.useState=function(t){return xt.H.useState(t)};K.useSyncExternalStore=function(t,e,n){return xt.H.useSyncExternalStore(t,e,n)};K.useTransition=function(){return xt.H.useTransition()};K.version="19.1.0"});var lr=ie((OA,Dp)=>{"use strict";Dp.exports=Mp()});var jp=ie(vt=>{"use strict";function Pu(t,e){var n=t.length;t.push(e);t:for(;0<n;){var l=n-1>>>1,i=t[l];if(0<Da(i,e))t[l]=e,t[n]=i,n=l;else break t}}function Fe(t){return t.length===0?null:t[0]}function Ra(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var l=0,i=t.length,r=i>>>1;l<r;){var a=2*(l+1)-1,o=t[a],u=a+1,c=t[u];if(0>Da(o,n))u<i&&0>Da(c,o)?(t[l]=c,t[u]=n,l=u):(t[l]=o,t[a]=n,l=a);else if(u<i&&0>Da(c,n))t[l]=c,t[u]=n,l=u;else break t}}return e}function Da(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}vt.unstable_now=void 0;typeof performance=="object"&&typeof performance.now=="function"?(Op=performance,vt.unstable_now=function(){return Op.now()}):(Ku=Date,Rp=Ku.now(),vt.unstable_now=function(){return Ku.now()-Rp});var Op,Ku,Rp,cn=[],On=[],Gx=1,ze=null,$t=3,$u=!1,ir=!1,rr=!1,tc=!1,Lp=typeof setTimeout=="function"?setTimeout:null,Up=typeof clearTimeout=="function"?clearTimeout:null,_p=typeof setImmediate!="undefined"?setImmediate:null;function Oa(t){for(var e=Fe(On);e!==null;){if(e.callback===null)Ra(On);else if(e.startTime<=t)Ra(On),e.sortIndex=e.expirationTime,Pu(cn,e);else break;e=Fe(On)}}function ec(t){if(rr=!1,Oa(t),!ir)if(Fe(cn)!==null)ir=!0,Wl||(Wl=!0,Jl());else{var e=Fe(On);e!==null&&nc(ec,e.startTime-t)}}var Wl=!1,ar=-1,Bp=5,Hp=-1;function qp(){return tc?!0:!(vt.unstable_now()-Hp<Bp)}function Ju(){if(tc=!1,Wl){var t=vt.unstable_now();Hp=t;var e=!0;try{t:{ir=!1,rr&&(rr=!1,Up(ar),ar=-1),$u=!0;var n=$t;try{e:{for(Oa(t),ze=Fe(cn);ze!==null&&!(ze.expirationTime>t&&qp());){var l=ze.callback;if(typeof l=="function"){ze.callback=null,$t=ze.priorityLevel;var i=l(ze.expirationTime<=t);if(t=vt.unstable_now(),typeof i=="function"){ze.callback=i,Oa(t),e=!0;break e}ze===Fe(cn)&&Ra(cn),Oa(t)}else Ra(cn);ze=Fe(cn)}if(ze!==null)e=!0;else{var r=Fe(On);r!==null&&nc(ec,r.startTime-t),e=!1}}break t}finally{ze=null,$t=n,$u=!1}e=void 0}}finally{e?Jl():Wl=!1}}}var Jl;typeof _p=="function"?Jl=function(){_p(Ju)}:typeof MessageChannel!="undefined"?(Wu=new MessageChannel,Np=Wu.port2,Wu.port1.onmessage=Ju,Jl=function(){Np.postMessage(null)}):Jl=function(){Lp(Ju,0)};var Wu,Np;function nc(t,e){ar=Lp(function(){t(vt.unstable_now())},e)}vt.unstable_IdlePriority=5;vt.unstable_ImmediatePriority=1;vt.unstable_LowPriority=4;vt.unstable_NormalPriority=3;vt.unstable_Profiling=null;vt.unstable_UserBlockingPriority=2;vt.unstable_cancelCallback=function(t){t.callback=null};vt.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Bp=0<t?Math.floor(1e3/t):5};vt.unstable_getCurrentPriorityLevel=function(){return $t};vt.unstable_next=function(t){switch($t){case 1:case 2:case 3:var e=3;break;default:e=$t}var n=$t;$t=e;try{return t()}finally{$t=n}};vt.unstable_requestPaint=function(){tc=!0};vt.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=$t;$t=t;try{return e()}finally{$t=n}};vt.unstable_scheduleCallback=function(t,e,n){var l=vt.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?l+n:l):n=l,t){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return i=n+i,t={id:Gx++,callback:e,priorityLevel:t,startTime:n,expirationTime:i,sortIndex:-1},n>l?(t.sortIndex=n,Pu(On,t),Fe(cn)===null&&t===Fe(On)&&(rr?(Up(ar),ar=-1):rr=!0,nc(ec,n-l))):(t.sortIndex=i,Pu(cn,t),ir||$u||(ir=!0,Wl||(Wl=!0,Jl()))),t};vt.unstable_shouldYield=qp;vt.unstable_wrapCallback=function(t){var e=$t;return function(){var n=$t;$t=e;try{return t.apply(this,arguments)}finally{$t=n}}}});var Yp=ie((_A,Gp)=>{"use strict";Gp.exports=jp()});var Xp=ie(ae=>{"use strict";var Yx=lr();function Vp(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Rn(){}var re={d:{f:Rn,r:function(){throw Error(Vp(522))},D:Rn,C:Rn,L:Rn,m:Rn,X:Rn,S:Rn,M:Rn},p:0,findDOMNode:null},Vx=Symbol.for("react.portal");function Xx(t,e,n){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Vx,key:l==null?null:""+l,children:t,containerInfo:e,implementation:n}}var or=Yx.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function _a(t,e){if(t==="font")return"";if(typeof e=="string")return e==="use-credentials"?e:""}ae.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=re;ae.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)throw Error(Vp(299));return Xx(t,e,null,n)};ae.flushSync=function(t){var e=or.T,n=re.p;try{if(or.T=null,re.p=2,t)return t()}finally{or.T=e,re.p=n,re.d.f()}};ae.preconnect=function(t,e){typeof t=="string"&&(e?(e=e.crossOrigin,e=typeof e=="string"?e==="use-credentials"?e:"":void 0):e=null,re.d.C(t,e))};ae.prefetchDNS=function(t){typeof t=="string"&&re.d.D(t)};ae.preinit=function(t,e){if(typeof t=="string"&&e&&typeof e.as=="string"){var n=e.as,l=_a(n,e.crossOrigin),i=typeof e.integrity=="string"?e.integrity:void 0,r=typeof e.fetchPriority=="string"?e.fetchPriority:void 0;n==="style"?re.d.S(t,typeof e.precedence=="string"?e.precedence:void 0,{crossOrigin:l,integrity:i,fetchPriority:r}):n==="script"&&re.d.X(t,{crossOrigin:l,integrity:i,fetchPriority:r,nonce:typeof e.nonce=="string"?e.nonce:void 0})}};ae.preinitModule=function(t,e){if(typeof t=="string")if(typeof e=="object"&&e!==null){if(e.as==null||e.as==="script"){var n=_a(e.as,e.crossOrigin);re.d.M(t,{crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0})}}else e==null&&re.d.M(t)};ae.preload=function(t,e){if(typeof t=="string"&&typeof e=="object"&&e!==null&&typeof e.as=="string"){var n=e.as,l=_a(n,e.crossOrigin);re.d.L(t,n,{crossOrigin:l,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0,type:typeof e.type=="string"?e.type:void 0,fetchPriority:typeof e.fetchPriority=="string"?e.fetchPriority:void 0,referrerPolicy:typeof e.referrerPolicy=="string"?e.referrerPolicy:void 0,imageSrcSet:typeof e.imageSrcSet=="string"?e.imageSrcSet:void 0,imageSizes:typeof e.imageSizes=="string"?e.imageSizes:void 0,media:typeof e.media=="string"?e.media:void 0})}};ae.preloadModule=function(t,e){if(typeof t=="string")if(e){var n=_a(e.as,e.crossOrigin);re.d.m(t,{as:typeof e.as=="string"&&e.as!=="script"?e.as:void 0,crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0})}else re.d.m(t)};ae.requestFormReset=function(t){re.d.r(t)};ae.unstable_batchedUpdates=function(t,e){return t(e)};ae.useFormState=function(t,e,n){return or.H.useFormState(t,e,n)};ae.useFormStatus=function(){return or.H.useHostTransitionStatus()};ae.version="19.1.0"});var Ip=ie((LA,Zp)=>{"use strict";function Qp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qp)}catch(t){console.error(t)}}Qp(),Zp.exports=Xp()});var Ky=ie(nu=>{"use strict";var jt=Yp(),dd=lr(),Qx=Ip();function M(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function gd(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Fr(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function yd(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Fp(t){if(Fr(t)!==t)throw Error(M(188))}function Zx(t){var e=t.alternate;if(!e){if(e=Fr(t),e===null)throw Error(M(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return Fp(i),t;if(r===l)return Fp(i),e;r=r.sibling}throw Error(M(188))}if(n.return!==l.return)n=i,l=r;else{for(var a=!1,o=i.child;o;){if(o===n){a=!0,n=i,l=r;break}if(o===l){a=!0,l=i,n=r;break}o=o.sibling}if(!a){for(o=r.child;o;){if(o===n){a=!0,n=r,l=i;break}if(o===l){a=!0,l=r,n=i;break}o=o.sibling}if(!a)throw Error(M(189))}}if(n.alternate!==l)throw Error(M(190))}if(n.tag!==3)throw Error(M(188));return n.stateNode.current===n?t:e}function bd(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=bd(t),e!==null)return e;t=t.sibling}return null}var yt=Object.assign,Ix=Symbol.for("react.element"),Na=Symbol.for("react.transitional.element"),gr=Symbol.for("react.portal"),ii=Symbol.for("react.fragment"),xd=Symbol.for("react.strict_mode"),Nc=Symbol.for("react.profiler"),Fx=Symbol.for("react.provider"),vd=Symbol.for("react.consumer"),hn=Symbol.for("react.context"),Ms=Symbol.for("react.forward_ref"),Lc=Symbol.for("react.suspense"),Uc=Symbol.for("react.suspense_list"),Ds=Symbol.for("react.memo"),Ln=Symbol.for("react.lazy");Symbol.for("react.scope");var Bc=Symbol.for("react.activity");Symbol.for("react.legacy_hidden");Symbol.for("react.tracing_marker");var Kx=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var Kp=Symbol.iterator;function ur(t){return t===null||typeof t!="object"?null:(t=Kp&&t[Kp]||t["@@iterator"],typeof t=="function"?t:null)}var Jx=Symbol.for("react.client.reference");function Hc(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Jx?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case ii:return"Fragment";case Nc:return"Profiler";case xd:return"StrictMode";case Lc:return"Suspense";case Uc:return"SuspenseList";case Bc:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case gr:return"Portal";case hn:return(t.displayName||"Context")+".Provider";case vd:return(t._context.displayName||"Context")+".Consumer";case Ms:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Ds:return e=t.displayName||null,e!==null?e:Hc(t.type)||"Memo";case Ln:e=t._payload,t=t._init;try{return Hc(t(e))}catch(n){}}return null}var yr=Array.isArray,Q=dd.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ot=Qx.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,yl={pending:!1,data:null,method:null,action:null},qc=[],ri=-1;function en(t){return{current:t}}function Zt(t){0>ri||(t.current=qc[ri],qc[ri]=null,ri--)}function kt(t,e){ri++,qc[ri]=t.current,t.current=e}var Pe=en(null),Nr=en(null),Qn=en(null),so=en(null);function fo(t,e){switch(kt(Qn,e),kt(Nr,t),kt(Pe,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?ed(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=ed(e),t=By(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Zt(Pe),kt(Pe,t)}function Ei(){Zt(Pe),Zt(Nr),Zt(Qn)}function jc(t){t.memoizedState!==null&&kt(so,t);var e=Pe.current,n=By(e,t.type);e!==n&&(kt(Nr,t),kt(Pe,n))}function mo(t){Nr.current===t&&(Zt(Pe),Zt(Nr)),so.current===t&&(Zt(so),Xr._currentValue=yl)}var Gc=Object.prototype.hasOwnProperty,Os=jt.unstable_scheduleCallback,lc=jt.unstable_cancelCallback,Wx=jt.unstable_shouldYield,Px=jt.unstable_requestPaint,$e=jt.unstable_now,$x=jt.unstable_getCurrentPriorityLevel,Sd=jt.unstable_ImmediatePriority,kd=jt.unstable_UserBlockingPriority,po=jt.unstable_NormalPriority,tv=jt.unstable_LowPriority,wd=jt.unstable_IdlePriority,ev=jt.log,nv=jt.unstable_setDisableYieldValue,Kr=null,ke=null;function Gn(t){if(typeof ev=="function"&&nv(t),ke&&typeof ke.setStrictMode=="function")try{ke.setStrictMode(Kr,t)}catch(e){}}var we=Math.clz32?Math.clz32:rv,lv=Math.log,iv=Math.LN2;function rv(t){return t>>>=0,t===0?32:31-(lv(t)/iv|0)|0}var La=256,Ua=4194304;function hl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Go(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,a=t.pingedLanes;t=t.warmLanes;var o=l&134217727;return o!==0?(l=o&~r,l!==0?i=hl(l):(a&=o,a!==0?i=hl(a):n||(n=o&~t,n!==0&&(i=hl(n))))):(o=l&~r,o!==0?i=hl(o):a!==0?i=hl(a):n||(n=l&~t,n!==0&&(i=hl(n)))),i===0?0:e!==0&&e!==i&&!(e&r)&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function Jr(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function av(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ed(){var t=La;return La<<=1,!(La&4194048)&&(La=256),t}function Td(){var t=Ua;return Ua<<=1,!(Ua&62914560)&&(Ua=4194304),t}function ic(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Wr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function ov(t,e,n,l,i,r){var a=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var o=t.entanglements,u=t.expirationTimes,c=t.hiddenUpdates;for(n=a&~n;0<n;){var s=31-we(n),f=1<<s;o[s]=0,u[s]=-1;var p=c[s];if(p!==null)for(c[s]=null,s=0;s<p.length;s++){var m=p[s];m!==null&&(m.lane&=-536870913)}n&=~f}l!==0&&Ad(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(a&~e))}function Ad(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-we(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function zd(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-we(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function Rs(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function _s(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function Cd(){var t=ot.p;return t!==0?t:(t=window.event,t===void 0?32:Iy(t.type))}function uv(t,e){var n=ot.p;try{return ot.p=t,e()}finally{ot.p=n}}var nl=Math.random().toString(36).slice(2),te="__reactFiber$"+nl,he="__reactProps$"+nl,Li="__reactContainer$"+nl,Yc="__reactEvents$"+nl,cv="__reactListeners$"+nl,sv="__reactHandles$"+nl,Jp="__reactResources$"+nl,Pr="__reactMarker$"+nl;function Ns(t){delete t[te],delete t[he],delete t[Yc],delete t[cv],delete t[sv]}function ai(t){var e=t[te];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Li]||n[te]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=id(t);t!==null;){if(n=t[te])return n;t=id(t)}return e}t=n,n=t.parentNode}return null}function Ui(t){if(t=t[te]||t[Li]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function br(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(M(33))}function gi(t){var e=t[Jp];return e||(e=t[Jp]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[Pr]=!0}var Md=new Set,Dd={};function Cl(t,e){Ti(t,e),Ti(t+"Capture",e)}function Ti(t,e){for(Dd[t]=e,t=0;t<e.length;t++)Md.add(e[t])}var fv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Wp={},Pp={};function mv(t){return Gc.call(Pp,t)?!0:Gc.call(Wp,t)?!1:fv.test(t)?Pp[t]=!0:(Wp[t]=!0,!1)}function Wa(t,e,n){if(mv(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ba(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function sn(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var rc,$p;function ei(t){if(rc===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);rc=e&&e[1]||"",$p=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+rc+t+$p}var ac=!1;function oc(t,e){if(!t||ac)return"";ac=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(m){var p=m}Reflect.construct(t,[],f)}else{try{f.call()}catch(m){p=m}t.call(f.prototype)}}else{try{throw Error()}catch(m){p=m}(f=t())&&typeof f.catch=="function"&&f.catch(function(){})}}catch(m){if(m&&p&&typeof m.stack=="string")return[m.stack,p.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),a=r[0],o=r[1];if(a&&o){var u=a.split(`
`),c=o.split(`
`);for(i=l=0;l<u.length&&!u[l].includes("DetermineComponentFrameRoot");)l++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(l===u.length||i===c.length)for(l=u.length-1,i=c.length-1;1<=l&&0<=i&&u[l]!==c[i];)i--;for(;1<=l&&0<=i;l--,i--)if(u[l]!==c[i]){if(l!==1||i!==1)do if(l--,i--,0>i||u[l]!==c[i]){var s=`
`+u[l].replace(" at new "," at ");return t.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",t.displayName)),s}while(1<=l&&0<=i);break}}}finally{ac=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ei(n):""}function pv(t){switch(t.tag){case 26:case 27:case 5:return ei(t.type);case 16:return ei("Lazy");case 13:return ei("Suspense");case 19:return ei("SuspenseList");case 0:case 15:return oc(t.type,!1);case 11:return oc(t.type.render,!1);case 1:return oc(t.type,!0);case 31:return ei("Activity");default:return""}}function th(t){try{var e="";do e+=pv(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Od(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function hv(t){var e=Od(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){l=""+a,r.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(a){l=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ho(t){t._valueTracker||(t._valueTracker=hv(t))}function Rd(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Od(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function go(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var dv=/[\n"\\]/g;function Re(t){return t.replace(dv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Vc(t,e,n,l,i,r,a,o){t.name="",a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"?t.type=a:t.removeAttribute("type"),e!=null?a==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Me(e)):t.value!==""+Me(e)&&(t.value=""+Me(e)):a!=="submit"&&a!=="reset"||t.removeAttribute("value"),e!=null?Xc(t,a,Me(e)):n!=null?Xc(t,a,Me(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.name=""+Me(o):t.removeAttribute("name")}function _d(t,e,n,l,i,r,a,o){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+Me(n):"",e=e!=null?""+Me(e):n,o||e===t.value||(t.value=e),t.defaultValue=e}l=l!=null?l:i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=o?t.checked:!!l,t.defaultChecked=!!l,a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.name=a)}function Xc(t,e,n){e==="number"&&go(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function yi(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+Me(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Nd(t,e,n){if(e!=null&&(e=""+Me(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Me(n):""}function Ld(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(M(92));if(yr(l)){if(1<l.length)throw Error(M(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=Me(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function Ai(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var gv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function eh(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||gv.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Ud(t,e,n){if(e!=null&&typeof e!="object")throw Error(M(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&eh(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&eh(t,r,e[r])}function Ls(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var yv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),bv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pa(t){return bv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Qc=null;function Us(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var oi=null,bi=null;function nh(t){var e=Ui(t);if(e&&(t=e.stateNode)){var n=t[he]||null;t:switch(t=e.stateNode,e.type){case"input":if(Vc(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Re(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[he]||null;if(!i)throw Error(M(90));Vc(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Rd(l)}break t;case"textarea":Nd(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&yi(t,!!n.multiple,e,!1)}}}var uc=!1;function Bd(t,e,n){if(uc)return t(e,n);uc=!0;try{var l=t(e);return l}finally{if(uc=!1,(oi!==null||bi!==null)&&(Wo(),oi&&(e=oi,t=bi,bi=oi=null,nh(e),t)))for(e=0;e<t.length;e++)nh(t[e])}}function Lr(t,e){var n=t.stateNode;if(n===null)return null;var l=n[he]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(M(231,e,typeof n));return n}var Sn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Zc=!1;if(Sn)try{Pl={},Object.defineProperty(Pl,"passive",{get:function(){Zc=!0}}),window.addEventListener("test",Pl,Pl),window.removeEventListener("test",Pl,Pl)}catch(t){Zc=!1}var Pl,Yn=null,Bs=null,$a=null;function Hd(){if($a)return $a;var t,e=Bs,n=e.length,l,i="value"in Yn?Yn.value:Yn.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(l=1;l<=a&&e[n-l]===i[r-l];l++);return $a=i.slice(t,1<l?1-l:void 0)}function to(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ha(){return!0}function lh(){return!1}function de(t){function e(n,l,i,r,a){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=a,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(n=t[o],this[o]=n?n(r):r[o]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ha:lh,this.isPropagationStopped=lh,this}return yt(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ha)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ha)},persist:function(){},isPersistent:Ha}),e}var Ml={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yo=de(Ml),$r=yt({},Ml,{view:0,detail:0}),xv=de($r),cc,sc,cr,Vo=yt({},$r,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hs,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==cr&&(cr&&t.type==="mousemove"?(cc=t.screenX-cr.screenX,sc=t.screenY-cr.screenY):sc=cc=0,cr=t),cc)},movementY:function(t){return"movementY"in t?t.movementY:sc}}),ih=de(Vo),vv=yt({},Vo,{dataTransfer:0}),Sv=de(vv),kv=yt({},$r,{relatedTarget:0}),fc=de(kv),wv=yt({},Ml,{animationName:0,elapsedTime:0,pseudoElement:0}),Ev=de(wv),Tv=yt({},Ml,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Av=de(Tv),zv=yt({},Ml,{data:0}),rh=de(zv),Cv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Mv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ov(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Dv[t])?!!e[t]:!1}function Hs(){return Ov}var Rv=yt({},$r,{key:function(t){if(t.key){var e=Cv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=to(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Mv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hs,charCode:function(t){return t.type==="keypress"?to(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?to(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),_v=de(Rv),Nv=yt({},Vo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ah=de(Nv),Lv=yt({},$r,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hs}),Uv=de(Lv),Bv=yt({},Ml,{propertyName:0,elapsedTime:0,pseudoElement:0}),Hv=de(Bv),qv=yt({},Vo,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),jv=de(qv),Gv=yt({},Ml,{newState:0,oldState:0}),Yv=de(Gv),Vv=[9,13,27,32],qs=Sn&&"CompositionEvent"in window,vr=null;Sn&&"documentMode"in document&&(vr=document.documentMode);var Xv=Sn&&"TextEvent"in window&&!vr,qd=Sn&&(!qs||vr&&8<vr&&11>=vr),oh=" ",uh=!1;function jd(t,e){switch(t){case"keyup":return Vv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gd(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ui=!1;function Qv(t,e){switch(t){case"compositionend":return Gd(e);case"keypress":return e.which!==32?null:(uh=!0,oh);case"textInput":return t=e.data,t===oh&&uh?null:t;default:return null}}function Zv(t,e){if(ui)return t==="compositionend"||!qs&&jd(t,e)?(t=Hd(),$a=Bs=Yn=null,ui=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return qd&&e.locale!=="ko"?null:e.data;default:return null}}var Iv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ch(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Iv[t.type]:e==="textarea"}function Yd(t,e,n,l){oi?bi?bi.push(l):bi=[l]:oi=l,e=No(e,"onChange"),0<e.length&&(n=new Yo("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Sr=null,Ur=null;function Fv(t){Ny(t,0)}function Xo(t){var e=br(t);if(Rd(e))return t}function sh(t,e){if(t==="change")return e}var Vd=!1;Sn&&(Sn?(ja="oninput"in document,ja||(mc=document.createElement("div"),mc.setAttribute("oninput","return;"),ja=typeof mc.oninput=="function"),qa=ja):qa=!1,Vd=qa&&(!document.documentMode||9<document.documentMode));var qa,ja,mc;function fh(){Sr&&(Sr.detachEvent("onpropertychange",Xd),Ur=Sr=null)}function Xd(t){if(t.propertyName==="value"&&Xo(Ur)){var e=[];Yd(e,Ur,t,Us(t)),Bd(Fv,e)}}function Kv(t,e,n){t==="focusin"?(fh(),Sr=e,Ur=n,Sr.attachEvent("onpropertychange",Xd)):t==="focusout"&&fh()}function Jv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Xo(Ur)}function Wv(t,e){if(t==="click")return Xo(e)}function Pv(t,e){if(t==="input"||t==="change")return Xo(e)}function $v(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ae=typeof Object.is=="function"?Object.is:$v;function Br(t,e){if(Ae(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Gc.call(e,i)||!Ae(t[i],e[i]))return!1}return!0}function mh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ph(t,e){var n=mh(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=mh(n)}}function Qd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Qd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Zd(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=go(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(l){n=!1}if(n)t=e.contentWindow;else break;e=go(t.document)}return e}function js(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var tS=Sn&&"documentMode"in document&&11>=document.documentMode,ci=null,Ic=null,kr=null,Fc=!1;function hh(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Fc||ci==null||ci!==go(l)||(l=ci,"selectionStart"in l&&js(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),kr&&Br(kr,l)||(kr=l,l=No(Ic,"onSelect"),0<l.length&&(e=new Yo("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=ci)))}function pl(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var si={animationend:pl("Animation","AnimationEnd"),animationiteration:pl("Animation","AnimationIteration"),animationstart:pl("Animation","AnimationStart"),transitionrun:pl("Transition","TransitionRun"),transitionstart:pl("Transition","TransitionStart"),transitioncancel:pl("Transition","TransitionCancel"),transitionend:pl("Transition","TransitionEnd")},pc={},Id={};Sn&&(Id=document.createElement("div").style,"AnimationEvent"in window||(delete si.animationend.animation,delete si.animationiteration.animation,delete si.animationstart.animation),"TransitionEvent"in window||delete si.transitionend.transition);function Dl(t){if(pc[t])return pc[t];if(!si[t])return t;var e=si[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Id)return pc[t]=e[n];return t}var Fd=Dl("animationend"),Kd=Dl("animationiteration"),Jd=Dl("animationstart"),eS=Dl("transitionrun"),nS=Dl("transitionstart"),lS=Dl("transitioncancel"),Wd=Dl("transitionend"),Pd=new Map,Kc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Kc.push("scrollEnd");function Xe(t,e){Pd.set(t,e),Cl(e,[t])}var dh=new WeakMap;function _e(t,e){if(typeof t=="object"&&t!==null){var n=dh.get(t);return n!==void 0?n:(e={value:t,source:e,stack:th(e)},dh.set(t,e),e)}return{value:t,source:e,stack:th(e)}}var Ce=[],fi=0,Gs=0;function Qo(){for(var t=fi,e=Gs=fi=0;e<t;){var n=Ce[e];Ce[e++]=null;var l=Ce[e];Ce[e++]=null;var i=Ce[e];Ce[e++]=null;var r=Ce[e];if(Ce[e++]=null,l!==null&&i!==null){var a=l.pending;a===null?i.next=i:(i.next=a.next,a.next=i),l.pending=i}r!==0&&$d(n,i,r)}}function Zo(t,e,n,l){Ce[fi++]=t,Ce[fi++]=e,Ce[fi++]=n,Ce[fi++]=l,Gs|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Ys(t,e,n,l){return Zo(t,e,n,l),yo(t)}function Bi(t,e){return Zo(t,null,null,e),yo(t)}function $d(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-we(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function yo(t){if(50<Rr)throw Rr=0,gs=null,Error(M(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var mi={};function iS(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(t,e,n,l){return new iS(t,e,n,l)}function Vs(t){return t=t.prototype,!(!t||!t.isReactComponent)}function xn(t,e){var n=t.alternate;return n===null?(n=Se(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function tg(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function eo(t,e,n,l,i,r){var a=0;if(l=t,typeof t=="function")Vs(t)&&(a=1);else if(typeof t=="string")a=ik(t,n,Pe.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Bc:return t=Se(31,n,e,i),t.elementType=Bc,t.lanes=r,t;case ii:return bl(n.children,i,r,e);case xd:a=8,i|=24;break;case Nc:return t=Se(12,n,e,i|2),t.elementType=Nc,t.lanes=r,t;case Lc:return t=Se(13,n,e,i),t.elementType=Lc,t.lanes=r,t;case Uc:return t=Se(19,n,e,i),t.elementType=Uc,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Fx:case hn:a=10;break t;case vd:a=9;break t;case Ms:a=11;break t;case Ds:a=14;break t;case Ln:a=16,l=null;break t}a=29,n=Error(M(130,t===null?"null":typeof t,"")),l=null}return e=Se(a,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function bl(t,e,n,l){return t=Se(7,t,l,e),t.lanes=n,t}function hc(t,e,n){return t=Se(6,t,null,e),t.lanes=n,t}function dc(t,e,n){return e=Se(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var pi=[],hi=0,bo=null,xo=0,De=[],Oe=0,xl=null,dn=1,gn="";function dl(t,e){pi[hi++]=xo,pi[hi++]=bo,bo=t,xo=e}function eg(t,e,n){De[Oe++]=dn,De[Oe++]=gn,De[Oe++]=xl,xl=t;var l=dn;t=gn;var i=32-we(l)-1;l&=~(1<<i),n+=1;var r=32-we(e)+i;if(30<r){var a=i-i%5;r=(l&(1<<a)-1).toString(32),l>>=a,i-=a,dn=1<<32-we(e)+i|n<<i|l,gn=r+t}else dn=1<<r|n<<i|l,gn=t}function Xs(t){t.return!==null&&(dl(t,1),eg(t,1,0))}function Qs(t){for(;t===bo;)bo=pi[--hi],pi[hi]=null,xo=pi[--hi],pi[hi]=null;for(;t===xl;)xl=De[--Oe],De[Oe]=null,gn=De[--Oe],De[Oe]=null,dn=De[--Oe],De[Oe]=null}var oe=null,At=null,at=!1,vl=null,Je=!1,Jc=Error(M(519));function El(t){var e=Error(M(418,""));throw Hr(_e(e,t)),Jc}function gh(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[te]=t,e[he]=l,n){case"dialog":$("cancel",e),$("close",e);break;case"iframe":case"object":case"embed":$("load",e);break;case"video":case"audio":for(n=0;n<Gr.length;n++)$(Gr[n],e);break;case"source":$("error",e);break;case"img":case"image":case"link":$("error",e),$("load",e);break;case"details":$("toggle",e);break;case"input":$("invalid",e),_d(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ho(e);break;case"select":$("invalid",e);break;case"textarea":$("invalid",e),Ld(e,l.value,l.defaultValue,l.children),ho(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Uy(e.textContent,n)?(l.popover!=null&&($("beforetoggle",e),$("toggle",e)),l.onScroll!=null&&$("scroll",e),l.onScrollEnd!=null&&$("scrollend",e),l.onClick!=null&&(e.onclick=tu),e=!0):e=!1,e||El(t)}function yh(t){for(oe=t.return;oe;)switch(oe.tag){case 5:case 13:Je=!1;return;case 27:case 3:Je=!0;return;default:oe=oe.return}}function sr(t){if(t!==oe)return!1;if(!at)return yh(t),at=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ks(t.type,t.memoizedProps)),n=!n),n&&At&&El(t),yh(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(M(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){At=Ve(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}At=null}}else e===27?(e=At,ll(t.type)?(t=Ts,Ts=null,At=t):At=e):At=oe?Ve(t.stateNode.nextSibling):null;return!0}function ta(){At=oe=null,at=!1}function bh(){var t=vl;return t!==null&&(pe===null?pe=t:pe.push.apply(pe,t),vl=null),t}function Hr(t){vl===null?vl=[t]:vl.push(t)}var Wc=en(null),Ol=null,yn=null;function Bn(t,e,n){kt(Wc,e._currentValue),e._currentValue=n}function vn(t){t._currentValue=Wc.current,Zt(Wc)}function Pc(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function $c(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var a=i.child;r=r.firstContext;t:for(;r!==null;){var o=r;r=i;for(var u=0;u<e.length;u++)if(o.context===e[u]){r.lanes|=n,o=r.alternate,o!==null&&(o.lanes|=n),Pc(r.return,n,t),l||(a=null);break t}r=o.next}}else if(i.tag===18){if(a=i.return,a===null)throw Error(M(341));a.lanes|=n,r=a.alternate,r!==null&&(r.lanes|=n),Pc(a,n,t),a=null}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}}function ea(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if(i.flags&524288)r=!0;else if(i.flags&262144)break}if(i.tag===10){var a=i.alternate;if(a===null)throw Error(M(387));if(a=a.memoizedProps,a!==null){var o=i.type;Ae(i.pendingProps.value,a.value)||(t!==null?t.push(o):t=[o])}}else if(i===so.current){if(a=i.alternate,a===null)throw Error(M(387));a.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Xr):t=[Xr])}i=i.return}t!==null&&$c(e,t,n,l),e.flags|=262144}function vo(t){for(t=t.firstContext;t!==null;){if(!Ae(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Tl(t){Ol=t,yn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ee(t){return ng(Ol,t)}function Ga(t,e){return Ol===null&&Tl(t),ng(t,e)}function ng(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},yn===null){if(t===null)throw Error(M(308));yn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else yn=yn.next=e;return n}var rS=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},aS=jt.unstable_scheduleCallback,oS=jt.unstable_NormalPriority,Ht={$$typeof:hn,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zs(){return{controller:new rS,data:new Map,refCount:0}}function na(t){t.refCount--,t.refCount===0&&aS(oS,function(){t.controller.abort()})}var wr=null,ts=0,zi=0,xi=null;function uS(t,e){if(wr===null){var n=wr=[];ts=0,zi=gf(),xi={status:"pending",value:void 0,then:function(l){n.push(l)}}}return ts++,e.then(xh,xh),e}function xh(){if(--ts===0&&wr!==null){xi!==null&&(xi.status="fulfilled");var t=wr;wr=null,zi=0,xi=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function cS(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var vh=Q.S;Q.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&uS(t,e),vh!==null&&vh(t,e)};var Sl=en(null);function Is(){var t=Sl.current;return t!==null?t:dt.pooledCache}function no(t,e){e===null?kt(Sl,Sl.current):kt(Sl,e.pool)}function lg(){var t=Is();return t===null?null:{parent:Ht._currentValue,pool:t}}var la=Error(M(460)),ig=Error(M(474)),Io=Error(M(542)),es={then:function(){}};function Sh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ya(){}function rg(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ya,Ya),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,wh(t),t;default:if(typeof e.status=="string")e.then(Ya,Ya);else{if(t=dt,t!==null&&100<t.shellSuspendCounter)throw Error(M(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,wh(t),t}throw Er=e,la}}var Er=null;function kh(){if(Er===null)throw Error(M(459));var t=Er;return Er=null,t}function wh(t){if(t===la||t===Io)throw Error(M(483))}var Un=!1;function Fs(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ns(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Zn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function In(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,ct&2){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=yo(t),$d(t,null,n),e}return Zo(t,l,e,n),yo(t)}function Tr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,zd(t,n)}}function gc(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var a={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=a:r=r.next=a,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var ls=!1;function Ar(){if(ls){var t=xi;if(t!==null)throw t}}function zr(t,e,n,l){ls=!1;var i=t.updateQueue;Un=!1;var r=i.firstBaseUpdate,a=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var u=o,c=u.next;u.next=null,a===null?r=c:a.next=c,a=u;var s=t.alternate;s!==null&&(s=s.updateQueue,o=s.lastBaseUpdate,o!==a&&(o===null?s.firstBaseUpdate=c:o.next=c,s.lastBaseUpdate=u))}if(r!==null){var f=i.baseState;a=0,s=c=u=null,o=r;do{var p=o.lane&-536870913,m=p!==o.lane;if(m?(nt&p)===p:(l&p)===p){p!==0&&p===zi&&(ls=!0),s!==null&&(s=s.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});t:{var y=t,v=o;p=e;var E=n;switch(v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(E,f,p);break t}f=y;break t;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,p=typeof y=="function"?y.call(E,f,p):y,p==null)break t;f=yt({},f,p);break t;case 2:Un=!0}}p=o.callback,p!==null&&(t.flags|=64,m&&(t.flags|=8192),m=i.callbacks,m===null?i.callbacks=[p]:m.push(p))}else m={lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},s===null?(c=s=m,u=f):s=s.next=m,a|=p;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;m=o,o=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);s===null&&(u=f),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=s,r===null&&(i.shared.lanes=0),el|=a,t.lanes=a,t.memoizedState=f}}function ag(t,e){if(typeof t!="function")throw Error(M(191,t));t.call(e)}function og(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)ag(n[t],e)}var Ci=en(null),So=en(0);function Eh(t,e){t=En,kt(So,t),kt(Ci,e),En=t|e.baseLanes}function is(){kt(So,En),kt(Ci,Ci.current)}function Ks(){En=So.current,Zt(Ci),Zt(So)}var $n=0,J=null,mt=null,Ut=null,ko=!1,vi=!1,Al=!1,wo=0,qr=0,Si=null,sS=0;function Ot(){throw Error(M(321))}function Js(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Ae(t[n],e[n]))return!1;return!0}function Ws(t,e,n,l,i,r){return $n=r,J=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Q.H=t===null||t.memoizedState===null?Hg:qg,Al=!1,r=n(l,i),Al=!1,vi&&(r=cg(e,n,l,i)),ug(t),r}function ug(t){Q.H=Eo;var e=mt!==null&&mt.next!==null;if($n=0,Ut=mt=J=null,ko=!1,qr=0,Si=null,e)throw Error(M(300));t===null||Qt||(t=t.dependencies,t!==null&&vo(t)&&(Qt=!0))}function cg(t,e,n,l){J=t;var i=0;do{if(vi&&(Si=null),qr=0,vi=!1,25<=i)throw Error(M(301));if(i+=1,Ut=mt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}Q.H=yS,r=e(n,l)}while(vi);return r}function fS(){var t=Q.H,e=t.useState()[0];return e=typeof e.then=="function"?ia(e):e,t=t.useState()[0],(mt!==null?mt.memoizedState:null)!==t&&(J.flags|=1024),e}function Ps(){var t=wo!==0;return wo=0,t}function $s(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function tf(t){if(ko){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ko=!1}$n=0,Ut=mt=J=null,vi=!1,qr=wo=0,Si=null}function fe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ut===null?J.memoizedState=Ut=t:Ut=Ut.next=t,Ut}function Bt(){if(mt===null){var t=J.alternate;t=t!==null?t.memoizedState:null}else t=mt.next;var e=Ut===null?J.memoizedState:Ut.next;if(e!==null)Ut=e,mt=t;else{if(t===null)throw J.alternate===null?Error(M(467)):Error(M(310));mt=t,t={memoizedState:mt.memoizedState,baseState:mt.baseState,baseQueue:mt.baseQueue,queue:mt.queue,next:null},Ut===null?J.memoizedState=Ut=t:Ut=Ut.next=t}return Ut}function ef(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ia(t){var e=qr;return qr+=1,Si===null&&(Si=[]),t=rg(Si,t,e),e=J,(Ut===null?e.memoizedState:Ut.next)===null&&(e=e.alternate,Q.H=e===null||e.memoizedState===null?Hg:qg),t}function Fo(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ia(t);if(t.$$typeof===hn)return ee(t)}throw Error(M(438,String(t)))}function nf(t){var e=null,n=J.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=J.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=ef(),J.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Kx;return e.index++,n}function kn(t,e){return typeof e=="function"?e(t):e}function lo(t){var e=Bt();return lf(e,mt,t)}function lf(t,e,n){var l=t.queue;if(l===null)throw Error(M(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var a=i.next;i.next=r.next,r.next=a}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var o=a=null,u=null,c=e,s=!1;do{var f=c.lane&-536870913;if(f!==c.lane?(nt&f)===f:($n&f)===f){var p=c.revertLane;if(p===0)u!==null&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===zi&&(s=!0);else if(($n&p)===p){c=c.next,p===zi&&(s=!0);continue}else f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},u===null?(o=u=f,a=r):u=u.next=f,J.lanes|=p,el|=p;f=c.action,Al&&n(r,f),r=c.hasEagerState?c.eagerState:n(r,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},u===null?(o=u=p,a=r):u=u.next=p,J.lanes|=f,el|=f;c=c.next}while(c!==null&&c!==e);if(u===null?a=r:u.next=o,!Ae(r,t.memoizedState)&&(Qt=!0,s&&(n=xi,n!==null)))throw n;t.memoizedState=r,t.baseState=a,t.baseQueue=u,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function yc(t){var e=Bt(),n=e.queue;if(n===null)throw Error(M(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do r=t(r,a.action),a=a.next;while(a!==i);Ae(r,e.memoizedState)||(Qt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function sg(t,e,n){var l=J,i=Bt(),r=at;if(r){if(n===void 0)throw Error(M(407));n=n()}else n=e();var a=!Ae((mt||i).memoizedState,n);a&&(i.memoizedState=n,Qt=!0),i=i.queue;var o=pg.bind(null,l,i,t);if(ra(2048,8,o,[t]),i.getSnapshot!==e||a||Ut!==null&&Ut.memoizedState.tag&1){if(l.flags|=2048,Mi(9,Ko(),mg.bind(null,l,i,n,e),null),dt===null)throw Error(M(349));r||$n&124||fg(l,e,n)}return n}function fg(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=J.updateQueue,e===null?(e=ef(),J.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function mg(t,e,n,l){e.value=n,e.getSnapshot=l,hg(e)&&dg(t)}function pg(t,e,n){return n(function(){hg(e)&&dg(t)})}function hg(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Ae(t,n)}catch(l){return!0}}function dg(t){var e=Bi(t,2);e!==null&&Te(e,t,2)}function rs(t){var e=fe();if(typeof t=="function"){var n=t;if(t=n(),Al){Gn(!0);try{n()}finally{Gn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:t},e}function gg(t,e,n,l){return t.baseState=n,lf(t,mt,typeof l=="function"?l:kn)}function mS(t,e,n,l,i){if(Jo(t))throw Error(M(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(a){r.listeners.push(a)}};Q.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,yg(e,r)):(r.next=n.next,e.pending=n.next=r)}}function yg(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=Q.T,a={};Q.T=a;try{var o=n(i,l),u=Q.S;u!==null&&u(a,o),Th(t,e,o)}catch(c){as(t,e,c)}finally{Q.T=r}}else try{r=n(i,l),Th(t,e,r)}catch(c){as(t,e,c)}}function Th(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Ah(t,e,l)},function(l){return as(t,e,l)}):Ah(t,e,n)}function Ah(t,e,n){e.status="fulfilled",e.value=n,bg(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,yg(t,n)))}function as(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,bg(e),e=e.next;while(e!==l)}t.action=null}function bg(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xg(t,e){return e}function zh(t,e){if(at){var n=dt.formState;if(n!==null){t:{var l=J;if(at){if(At){e:{for(var i=At,r=Je;i.nodeType!==8;){if(!r){i=null;break e}if(i=Ve(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){At=Ve(i.nextSibling),l=i.data==="F!";break t}}El(l)}l=!1}l&&(e=n[0])}}return n=fe(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xg,lastRenderedState:e},n.queue=l,n=Lg.bind(null,J,l),l.dispatch=n,l=rs(!1),r=uf.bind(null,J,!1,l.queue),l=fe(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=mS.bind(null,J,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function Ch(t){var e=Bt();return vg(e,mt,t)}function vg(t,e,n){if(e=lf(t,e,xg)[0],t=lo(kn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ia(e)}catch(a){throw a===la?Io:a}else l=e;e=Bt();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(J.flags|=2048,Mi(9,Ko(),pS.bind(null,i,n),null)),[l,r,t]}function pS(t,e){t.action=e}function Mh(t){var e=Bt(),n=mt;if(n!==null)return vg(e,n,t);Bt(),e=e.memoizedState,n=Bt();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function Mi(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=J.updateQueue,e===null&&(e=ef(),J.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Ko(){return{destroy:void 0,resource:void 0}}function Sg(){return Bt().memoizedState}function io(t,e,n,l){var i=fe();l=l===void 0?null:l,J.flags|=t,i.memoizedState=Mi(1|e,Ko(),n,l)}function ra(t,e,n,l){var i=Bt();l=l===void 0?null:l;var r=i.memoizedState.inst;mt!==null&&l!==null&&Js(l,mt.memoizedState.deps)?i.memoizedState=Mi(e,r,n,l):(J.flags|=t,i.memoizedState=Mi(1|e,r,n,l))}function Dh(t,e){io(8390656,8,t,e)}function kg(t,e){ra(2048,8,t,e)}function wg(t,e){return ra(4,2,t,e)}function Eg(t,e){return ra(4,4,t,e)}function Tg(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Ag(t,e,n){n=n!=null?n.concat([t]):null,ra(4,4,Tg.bind(null,e,t),n)}function rf(){}function zg(t,e){var n=Bt();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Js(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Cg(t,e){var n=Bt();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Js(e,l[1]))return l[0];if(l=t(),Al){Gn(!0);try{t()}finally{Gn(!1)}}return n.memoizedState=[l,e],l}function af(t,e,n){return n===void 0||$n&1073741824?t.memoizedState=e:(t.memoizedState=n,t=by(),J.lanes|=t,el|=t,n)}function Mg(t,e,n,l){return Ae(n,e)?n:Ci.current!==null?(t=af(t,n,l),Ae(t,e)||(Qt=!0),t):$n&42?(t=by(),J.lanes|=t,el|=t,e):(Qt=!0,t.memoizedState=n)}function Dg(t,e,n,l,i){var r=ot.p;ot.p=r!==0&&8>r?r:8;var a=Q.T,o={};Q.T=o,uf(t,!1,e,n);try{var u=i(),c=Q.S;if(c!==null&&c(o,u),u!==null&&typeof u=="object"&&typeof u.then=="function"){var s=cS(u,l);Cr(t,e,s,Ee(t))}else Cr(t,e,l,Ee(t))}catch(f){Cr(t,e,{then:function(){},status:"rejected",reason:f},Ee())}finally{ot.p=r,Q.T=a}}function hS(){}function os(t,e,n,l){if(t.tag!==5)throw Error(M(476));var i=Og(t).queue;Dg(t,i,e,yl,n===null?hS:function(){return Rg(t),n(l)})}function Og(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:yl,baseState:yl,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:yl},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Rg(t){var e=Og(t).next.queue;Cr(t,e,{},Ee())}function of(){return ee(Xr)}function _g(){return Bt().memoizedState}function Ng(){return Bt().memoizedState}function dS(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Ee();t=Zn(n);var l=In(e,t,n);l!==null&&(Te(l,e,n),Tr(l,e,n)),e={cache:Zs()},t.payload=e;return}e=e.return}}function gS(t,e,n){var l=Ee();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Jo(t)?Ug(e,n):(n=Ys(t,e,n,l),n!==null&&(Te(n,t,l),Bg(n,e,l)))}function Lg(t,e,n){var l=Ee();Cr(t,e,n,l)}function Cr(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jo(t))Ug(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var a=e.lastRenderedState,o=r(a,n);if(i.hasEagerState=!0,i.eagerState=o,Ae(o,a))return Zo(t,e,i,0),dt===null&&Qo(),!1}catch(u){}finally{}if(n=Ys(t,e,i,l),n!==null)return Te(n,t,l),Bg(n,e,l),!0}return!1}function uf(t,e,n,l){if(l={lane:2,revertLane:gf(),action:l,hasEagerState:!1,eagerState:null,next:null},Jo(t)){if(e)throw Error(M(479))}else e=Ys(t,n,l,2),e!==null&&Te(e,t,2)}function Jo(t){var e=t.alternate;return t===J||e!==null&&e===J}function Ug(t,e){vi=ko=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Bg(t,e,n){if(n&4194048){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,zd(t,n)}}var Eo={readContext:ee,use:Fo,useCallback:Ot,useContext:Ot,useEffect:Ot,useImperativeHandle:Ot,useLayoutEffect:Ot,useInsertionEffect:Ot,useMemo:Ot,useReducer:Ot,useRef:Ot,useState:Ot,useDebugValue:Ot,useDeferredValue:Ot,useTransition:Ot,useSyncExternalStore:Ot,useId:Ot,useHostTransitionStatus:Ot,useFormState:Ot,useActionState:Ot,useOptimistic:Ot,useMemoCache:Ot,useCacheRefresh:Ot},Hg={readContext:ee,use:Fo,useCallback:function(t,e){return fe().memoizedState=[t,e===void 0?null:e],t},useContext:ee,useEffect:Dh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,io(4194308,4,Tg.bind(null,e,t),n)},useLayoutEffect:function(t,e){return io(4194308,4,t,e)},useInsertionEffect:function(t,e){io(4,2,t,e)},useMemo:function(t,e){var n=fe();e=e===void 0?null:e;var l=t();if(Al){Gn(!0);try{t()}finally{Gn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=fe();if(n!==void 0){var i=n(e);if(Al){Gn(!0);try{n(e)}finally{Gn(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=gS.bind(null,J,t),[l.memoizedState,t]},useRef:function(t){var e=fe();return t={current:t},e.memoizedState=t},useState:function(t){t=rs(t);var e=t.queue,n=Lg.bind(null,J,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:rf,useDeferredValue:function(t,e){var n=fe();return af(n,t,e)},useTransition:function(){var t=rs(!1);return t=Dg.bind(null,J,t.queue,!0,!1),fe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=J,i=fe();if(at){if(n===void 0)throw Error(M(407));n=n()}else{if(n=e(),dt===null)throw Error(M(349));nt&124||fg(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,Dh(pg.bind(null,l,r,t),[t]),l.flags|=2048,Mi(9,Ko(),mg.bind(null,l,r,n,e),null),n},useId:function(){var t=fe(),e=dt.identifierPrefix;if(at){var n=gn,l=dn;n=(l&~(1<<32-we(l)-1)).toString(32)+n,e="\xAB"+e+"R"+n,n=wo++,0<n&&(e+="H"+n.toString(32)),e+="\xBB"}else n=sS++,e="\xAB"+e+"r"+n.toString(32)+"\xBB";return t.memoizedState=e},useHostTransitionStatus:of,useFormState:zh,useActionState:zh,useOptimistic:function(t){var e=fe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=uf.bind(null,J,!0,n),n.dispatch=e,[t,e]},useMemoCache:nf,useCacheRefresh:function(){return fe().memoizedState=dS.bind(null,J)}},qg={readContext:ee,use:Fo,useCallback:zg,useContext:ee,useEffect:kg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Eg,useMemo:Cg,useReducer:lo,useRef:Sg,useState:function(){return lo(kn)},useDebugValue:rf,useDeferredValue:function(t,e){var n=Bt();return Mg(n,mt.memoizedState,t,e)},useTransition:function(){var t=lo(kn)[0],e=Bt().memoizedState;return[typeof t=="boolean"?t:ia(t),e]},useSyncExternalStore:sg,useId:_g,useHostTransitionStatus:of,useFormState:Ch,useActionState:Ch,useOptimistic:function(t,e){var n=Bt();return gg(n,mt,t,e)},useMemoCache:nf,useCacheRefresh:Ng},yS={readContext:ee,use:Fo,useCallback:zg,useContext:ee,useEffect:kg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Eg,useMemo:Cg,useReducer:yc,useRef:Sg,useState:function(){return yc(kn)},useDebugValue:rf,useDeferredValue:function(t,e){var n=Bt();return mt===null?af(n,t,e):Mg(n,mt.memoizedState,t,e)},useTransition:function(){var t=yc(kn)[0],e=Bt().memoizedState;return[typeof t=="boolean"?t:ia(t),e]},useSyncExternalStore:sg,useId:_g,useHostTransitionStatus:of,useFormState:Mh,useActionState:Mh,useOptimistic:function(t,e){var n=Bt();return mt!==null?gg(n,mt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:nf,useCacheRefresh:Ng},ki=null,jr=0;function Va(t){var e=jr;return jr+=1,ki===null&&(ki=[]),rg(ki,t,e)}function fr(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Xa(t,e){throw e.$$typeof===Ix?Error(M(525)):(t=Object.prototype.toString.call(e),Error(M(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Oh(t){var e=t._init;return e(t._payload)}function jg(t){function e(h,d){if(t){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!t)return null;for(;d!==null;)e(h,d),d=d.sibling;return null}function l(h){for(var d=new Map;h!==null;)h.key!==null?d.set(h.key,h):d.set(h.index,h),h=h.sibling;return d}function i(h,d){return h=xn(h,d),h.index=0,h.sibling=null,h}function r(h,d,g){return h.index=g,t?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=67108866,d):g):(h.flags|=67108866,d)):(h.flags|=1048576,d)}function a(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function o(h,d,g,S){return d===null||d.tag!==6?(d=hc(g,h.mode,S),d.return=h,d):(d=i(d,g),d.return=h,d)}function u(h,d,g,S){var z=g.type;return z===ii?s(h,d,g.props.children,S,g.key):d!==null&&(d.elementType===z||typeof z=="object"&&z!==null&&z.$$typeof===Ln&&Oh(z)===d.type)?(d=i(d,g.props),fr(d,g),d.return=h,d):(d=eo(g.type,g.key,g.props,null,h.mode,S),fr(d,g),d.return=h,d)}function c(h,d,g,S){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=dc(g,h.mode,S),d.return=h,d):(d=i(d,g.children||[]),d.return=h,d)}function s(h,d,g,S,z){return d===null||d.tag!==7?(d=bl(g,h.mode,S,z),d.return=h,d):(d=i(d,g),d.return=h,d)}function f(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=hc(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Na:return g=eo(d.type,d.key,d.props,null,h.mode,g),fr(g,d),g.return=h,g;case gr:return d=dc(d,h.mode,g),d.return=h,d;case Ln:var S=d._init;return d=S(d._payload),f(h,d,g)}if(yr(d)||ur(d))return d=bl(d,h.mode,g,null),d.return=h,d;if(typeof d.then=="function")return f(h,Va(d),g);if(d.$$typeof===hn)return f(h,Ga(h,d),g);Xa(h,d)}return null}function p(h,d,g,S){var z=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return z!==null?null:o(h,d,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:return g.key===z?u(h,d,g,S):null;case gr:return g.key===z?c(h,d,g,S):null;case Ln:return z=g._init,g=z(g._payload),p(h,d,g,S)}if(yr(g)||ur(g))return z!==null?null:s(h,d,g,S,null);if(typeof g.then=="function")return p(h,d,Va(g),S);if(g.$$typeof===hn)return p(h,d,Ga(h,g),S);Xa(h,g)}return null}function m(h,d,g,S,z){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return h=h.get(g)||null,o(d,h,""+S,z);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Na:return h=h.get(S.key===null?g:S.key)||null,u(d,h,S,z);case gr:return h=h.get(S.key===null?g:S.key)||null,c(d,h,S,z);case Ln:var w=S._init;return S=w(S._payload),m(h,d,g,S,z)}if(yr(S)||ur(S))return h=h.get(g)||null,s(d,h,S,z,null);if(typeof S.then=="function")return m(h,d,g,Va(S),z);if(S.$$typeof===hn)return m(h,d,g,Ga(d,S),z);Xa(d,S)}return null}function y(h,d,g,S){for(var z=null,w=null,D=d,T=d=0,q=null;D!==null&&T<g.length;T++){D.index>T?(q=D,D=null):q=D.sibling;var k=p(h,D,g[T],S);if(k===null){D===null&&(D=q);break}t&&D&&k.alternate===null&&e(h,D),d=r(k,d,T),w===null?z=k:w.sibling=k,w=k,D=q}if(T===g.length)return n(h,D),at&&dl(h,T),z;if(D===null){for(;T<g.length;T++)D=f(h,g[T],S),D!==null&&(d=r(D,d,T),w===null?z=D:w.sibling=D,w=D);return at&&dl(h,T),z}for(D=l(D);T<g.length;T++)q=m(D,h,T,g[T],S),q!==null&&(t&&q.alternate!==null&&D.delete(q.key===null?T:q.key),d=r(q,d,T),w===null?z=q:w.sibling=q,w=q);return t&&D.forEach(function(it){return e(h,it)}),at&&dl(h,T),z}function v(h,d,g,S){if(g==null)throw Error(M(151));for(var z=null,w=null,D=d,T=d=0,q=null,k=g.next();D!==null&&!k.done;T++,k=g.next()){D.index>T?(q=D,D=null):q=D.sibling;var it=p(h,D,k.value,S);if(it===null){D===null&&(D=q);break}t&&D&&it.alternate===null&&e(h,D),d=r(it,d,T),w===null?z=it:w.sibling=it,w=it,D=q}if(k.done)return n(h,D),at&&dl(h,T),z;if(D===null){for(;!k.done;T++,k=g.next())k=f(h,k.value,S),k!==null&&(d=r(k,d,T),w===null?z=k:w.sibling=k,w=k);return at&&dl(h,T),z}for(D=l(D);!k.done;T++,k=g.next())k=m(D,h,T,k.value,S),k!==null&&(t&&k.alternate!==null&&D.delete(k.key===null?T:k.key),d=r(k,d,T),w===null?z=k:w.sibling=k,w=k);return t&&D.forEach(function(L){return e(h,L)}),at&&dl(h,T),z}function E(h,d,g,S){if(typeof g=="object"&&g!==null&&g.type===ii&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:t:{for(var z=g.key;d!==null;){if(d.key===z){if(z=g.type,z===ii){if(d.tag===7){n(h,d.sibling),S=i(d,g.props.children),S.return=h,h=S;break t}}else if(d.elementType===z||typeof z=="object"&&z!==null&&z.$$typeof===Ln&&Oh(z)===d.type){n(h,d.sibling),S=i(d,g.props),fr(S,g),S.return=h,h=S;break t}n(h,d);break}else e(h,d);d=d.sibling}g.type===ii?(S=bl(g.props.children,h.mode,S,g.key),S.return=h,h=S):(S=eo(g.type,g.key,g.props,null,h.mode,S),fr(S,g),S.return=h,h=S)}return a(h);case gr:t:{for(z=g.key;d!==null;){if(d.key===z)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),S=i(d,g.children||[]),S.return=h,h=S;break t}else{n(h,d);break}else e(h,d);d=d.sibling}S=dc(g,h.mode,S),S.return=h,h=S}return a(h);case Ln:return z=g._init,g=z(g._payload),E(h,d,g,S)}if(yr(g))return y(h,d,g,S);if(ur(g)){if(z=ur(g),typeof z!="function")throw Error(M(150));return g=z.call(g),v(h,d,g,S)}if(typeof g.then=="function")return E(h,d,Va(g),S);if(g.$$typeof===hn)return E(h,d,Ga(h,g),S);Xa(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),S=i(d,g),S.return=h,h=S):(n(h,d),S=hc(g,h.mode,S),S.return=h,h=S),a(h)):n(h,d)}return function(h,d,g,S){try{jr=0;var z=E(h,d,g,S);return ki=null,z}catch(D){if(D===la||D===Io)throw D;var w=Se(29,D,null,h.mode);return w.lanes=S,w.return=h,w}finally{}}}var Di=jg(!0),Gg=jg(!1),Le=en(null),tn=null;function Hn(t){var e=t.alternate;kt(qt,qt.current&1),kt(Le,t),tn===null&&(e===null||Ci.current!==null||e.memoizedState!==null)&&(tn=t)}function Yg(t){if(t.tag===22){if(kt(qt,qt.current),kt(Le,t),tn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(tn=t)}}else qn(t)}function qn(){kt(qt,qt.current),kt(Le,Le.current)}function bn(t){Zt(Le),tn===t&&(tn=null),Zt(qt)}var qt=en(0);function To(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Es(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function bc(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:yt({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var us={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=Ee(),i=Zn(l);i.payload=e,n!=null&&(i.callback=n),e=In(t,i,l),e!==null&&(Te(e,t,l),Tr(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=Ee(),i=Zn(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=In(t,i,l),e!==null&&(Te(e,t,l),Tr(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Ee(),l=Zn(n);l.tag=2,e!=null&&(l.callback=e),e=In(t,l,n),e!==null&&(Te(e,t,n),Tr(e,t,n))}};function Rh(t,e,n,l,i,r,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,a):e.prototype&&e.prototype.isPureReactComponent?!Br(n,l)||!Br(i,r):!0}function _h(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&us.enqueueReplaceState(e,e.state,null)}function zl(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=yt({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var Ao=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Vg(t){Ao(t)}function Xg(t){console.error(t)}function Qg(t){Ao(t)}function zo(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Nh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function cs(t,e,n){return n=Zn(n),n.tag=3,n.payload={element:null},n.callback=function(){zo(t,e)},n}function Zg(t){return t=Zn(t),t.tag=3,t}function Ig(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){Nh(e,n,l)}}var a=n.stateNode;a!==null&&typeof a.componentDidCatch=="function"&&(t.callback=function(){Nh(e,n,l),typeof i!="function"&&(Fn===null?Fn=new Set([this]):Fn.add(this));var o=l.stack;this.componentDidCatch(l.value,{componentStack:o!==null?o:""})})}function bS(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ea(e,n,i,!0),n=Le.current,n!==null){switch(n.tag){case 13:return tn===null?ys():n.alternate===null&&zt===0&&(zt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===es?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Mc(t,l,i)),!1;case 22:return n.flags|=65536,l===es?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Mc(t,l,i)),!1}throw Error(M(435,n.tag))}return Mc(t,l,i),ys(),!1}if(at)return e=Le.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==Jc&&(t=Error(M(422),{cause:l}),Hr(_e(t,n)))):(l!==Jc&&(e=Error(M(423),{cause:l}),Hr(_e(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=_e(l,n),i=cs(t.stateNode,l,i),gc(t,i),zt!==4&&(zt=2)),!1;var r=Error(M(520),{cause:l});if(r=_e(r,n),Or===null?Or=[r]:Or.push(r),zt!==4&&(zt=2),e===null)return!0;l=_e(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=cs(n.stateNode,l,t),gc(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Fn===null||!Fn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Zg(i),Ig(i,t,n,l),gc(n,i),!1}n=n.return}while(n!==null);return!1}var Fg=Error(M(461)),Qt=!1;function Jt(t,e,n,l){e.child=t===null?Gg(e,null,n,l):Di(e,t.child,n,l)}function Lh(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var a={};for(var o in l)o!=="ref"&&(a[o]=l[o])}else a=l;return Tl(e),l=Ws(t,e,n,a,r,i),o=Ps(),t!==null&&!Qt?($s(t,e,i),wn(t,e,i)):(at&&o&&Xs(e),e.flags|=1,Jt(t,e,l,i),e.child)}function Uh(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!Vs(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Kg(t,e,r,l,i)):(t=eo(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!cf(t,i)){var a=r.memoizedProps;if(n=n.compare,n=n!==null?n:Br,n(a,l)&&t.ref===e.ref)return wn(t,e,i)}return e.flags|=1,t=xn(r,l),t.ref=e.ref,t.return=e,e.child=t}function Kg(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(Br(r,l)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=l=r,cf(t,i))t.flags&131072&&(Qt=!0);else return e.lanes=t.lanes,wn(t,e,i)}return ss(t,e,n,l,i)}function Jg(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if(e.flags&128){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return Bh(t,e,l,n)}if(n&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&no(e,r!==null?r.cachePool:null),r!==null?Eh(e,r):is(),Yg(e);else return e.lanes=e.childLanes=536870912,Bh(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(no(e,r.cachePool),Eh(e,r),qn(e),e.memoizedState=null):(t!==null&&no(e,null),is(),qn(e));return Jt(t,e,i,n),e.child}function Bh(t,e,n,l){var i=Is();return i=i===null?null:{parent:Ht._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&no(e,null),is(),Yg(e),t!==null&&ea(t,e,l,!0),null}function ro(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(M(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function ss(t,e,n,l,i){return Tl(e),n=Ws(t,e,n,l,void 0,i),l=Ps(),t!==null&&!Qt?($s(t,e,i),wn(t,e,i)):(at&&l&&Xs(e),e.flags|=1,Jt(t,e,n,i),e.child)}function Hh(t,e,n,l,i,r){return Tl(e),e.updateQueue=null,n=cg(e,l,n,i),ug(t),l=Ps(),t!==null&&!Qt?($s(t,e,r),wn(t,e,r)):(at&&l&&Xs(e),e.flags|=1,Jt(t,e,n,r),e.child)}function qh(t,e,n,l,i){if(Tl(e),e.stateNode===null){var r=mi,a=n.contextType;typeof a=="object"&&a!==null&&(r=ee(a)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=us,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Fs(e),a=n.contextType,r.context=typeof a=="object"&&a!==null?ee(a):mi,r.state=e.memoizedState,a=n.getDerivedStateFromProps,typeof a=="function"&&(bc(e,n,a,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(a=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),a!==r.state&&us.enqueueReplaceState(r,r.state,null),zr(e,l,r,i),Ar(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var o=e.memoizedProps,u=zl(n,o);r.props=u;var c=r.context,s=n.contextType;a=mi,typeof s=="object"&&s!==null&&(a=ee(s));var f=n.getDerivedStateFromProps;s=typeof f=="function"||typeof r.getSnapshotBeforeUpdate=="function",o=e.pendingProps!==o,s||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(o||c!==a)&&_h(e,r,l,a),Un=!1;var p=e.memoizedState;r.state=p,zr(e,l,r,i),Ar(),c=e.memoizedState,o||p!==c||Un?(typeof f=="function"&&(bc(e,n,f,l),c=e.memoizedState),(u=Un||Rh(e,n,u,l,p,c,a))?(s||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=c),r.props=l,r.state=c,r.context=a,l=u):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,ns(t,e),a=e.memoizedProps,s=zl(n,a),r.props=s,f=e.pendingProps,p=r.context,c=n.contextType,u=mi,typeof c=="object"&&c!==null&&(u=ee(c)),o=n.getDerivedStateFromProps,(c=typeof o=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(a!==f||p!==u)&&_h(e,r,l,u),Un=!1,p=e.memoizedState,r.state=p,zr(e,l,r,i),Ar();var m=e.memoizedState;a!==f||p!==m||Un||t!==null&&t.dependencies!==null&&vo(t.dependencies)?(typeof o=="function"&&(bc(e,n,o,l),m=e.memoizedState),(s=Un||Rh(e,n,s,l,p,m,u)||t!==null&&t.dependencies!==null&&vo(t.dependencies))?(c||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,m,u),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,m,u)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=m),r.props=l,r.state=m,r.context=u,l=s):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,ro(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Di(e,t.child,null,i),e.child=Di(e,null,n,i)):Jt(t,e,n,i),e.memoizedState=r.state,t=e.child):t=wn(t,e,i),t}function jh(t,e,n,l){return ta(),e.flags|=256,Jt(t,e,n,l),e.child}var xc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function vc(t){return{baseLanes:t,cachePool:lg()}}function Sc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ne),t}function Wg(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,a;if((a=r)||(a=t!==null&&t.memoizedState===null?!1:(qt.current&2)!==0),a&&(i=!0,e.flags&=-129),a=(e.flags&32)!==0,e.flags&=-33,t===null){if(at){if(i?Hn(e):qn(e),at){var o=At,u;if(u=o){t:{for(u=o,o=Je;u.nodeType!==8;){if(!o){o=null;break t}if(u=Ve(u.nextSibling),u===null){o=null;break t}}o=u}o!==null?(e.memoizedState={dehydrated:o,treeContext:xl!==null?{id:dn,overflow:gn}:null,retryLane:536870912,hydrationErrors:null},u=Se(18,null,null,0),u.stateNode=o,u.return=e,e.child=u,oe=e,At=null,u=!0):u=!1}u||El(e)}if(o=e.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return Es(o)?e.lanes=32:e.lanes=536870912,null;bn(e)}return o=l.children,l=l.fallback,i?(qn(e),i=e.mode,o=Co({mode:"hidden",children:o},i),l=bl(l,i,n,null),o.return=e,l.return=e,o.sibling=l,e.child=o,i=e.child,i.memoizedState=vc(n),i.childLanes=Sc(t,a,n),e.memoizedState=xc,l):(Hn(e),fs(e,o))}if(u=t.memoizedState,u!==null&&(o=u.dehydrated,o!==null)){if(r)e.flags&256?(Hn(e),e.flags&=-257,e=kc(t,e,n)):e.memoizedState!==null?(qn(e),e.child=t.child,e.flags|=128,e=null):(qn(e),i=l.fallback,o=e.mode,l=Co({mode:"visible",children:l.children},o),i=bl(i,o,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,Di(e,t.child,null,n),l=e.child,l.memoizedState=vc(n),l.childLanes=Sc(t,a,n),e.memoizedState=xc,e=i);else if(Hn(e),Es(o)){if(a=o.nextSibling&&o.nextSibling.dataset,a)var c=a.dgst;a=c,l=Error(M(419)),l.stack="",l.digest=a,Hr({value:l,source:null,stack:null}),e=kc(t,e,n)}else if(Qt||ea(t,e,n,!1),a=(n&t.childLanes)!==0,Qt||a){if(a=dt,a!==null&&(l=n&-n,l=l&42?1:Rs(l),l=l&(a.suspendedLanes|n)?0:l,l!==0&&l!==u.retryLane))throw u.retryLane=l,Bi(t,l),Te(a,t,l),Fg;o.data==="$?"||ys(),e=kc(t,e,n)}else o.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=u.treeContext,At=Ve(o.nextSibling),oe=e,at=!0,vl=null,Je=!1,t!==null&&(De[Oe++]=dn,De[Oe++]=gn,De[Oe++]=xl,dn=t.id,gn=t.overflow,xl=e),e=fs(e,l.children),e.flags|=4096);return e}return i?(qn(e),i=l.fallback,o=e.mode,u=t.child,c=u.sibling,l=xn(u,{mode:"hidden",children:l.children}),l.subtreeFlags=u.subtreeFlags&65011712,c!==null?i=xn(c,i):(i=bl(i,o,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,o=t.child.memoizedState,o===null?o=vc(n):(u=o.cachePool,u!==null?(c=Ht._currentValue,u=u.parent!==c?{parent:c,pool:c}:u):u=lg(),o={baseLanes:o.baseLanes|n,cachePool:u}),i.memoizedState=o,i.childLanes=Sc(t,a,n),e.memoizedState=xc,l):(Hn(e),n=t.child,t=n.sibling,n=xn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(a=e.deletions,a===null?(e.deletions=[t],e.flags|=16):a.push(t)),e.child=n,e.memoizedState=null,n)}function fs(t,e){return e=Co({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Co(t,e){return t=Se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function kc(t,e,n){return Di(e,t.child,null,n),t=fs(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Gh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Pc(t.return,e,n)}function wc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function Pg(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(Jt(t,e,l.children,n),l=qt.current,l&2)l=l&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Gh(t,n,e);else if(t.tag===19)Gh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(kt(qt,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&To(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),wc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&To(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}wc(e,!0,n,null,r);break;case"together":wc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function wn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),el|=e.lanes,!(n&e.childLanes))if(t!==null){if(ea(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(M(153));if(e.child!==null){for(t=e.child,n=xn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=xn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function cf(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&vo(t)))}function xS(t,e,n){switch(e.tag){case 3:fo(e,e.stateNode.containerInfo),Bn(e,Ht,t.memoizedState.cache),ta();break;case 27:case 5:jc(e);break;case 4:fo(e,e.stateNode.containerInfo);break;case 10:Bn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Hn(e),e.flags|=128,null):n&e.child.childLanes?Wg(t,e,n):(Hn(e),t=wn(t,e,n),t!==null?t.sibling:null);Hn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ea(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return Pg(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),kt(qt,qt.current),l)break;return null;case 22:case 23:return e.lanes=0,Jg(t,e,n);case 24:Bn(e,Ht,t.memoizedState.cache)}return wn(t,e,n)}function $g(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!cf(t,n)&&!(e.flags&128))return Qt=!1,xS(t,e,n);Qt=!!(t.flags&131072)}else Qt=!1,at&&e.flags&1048576&&eg(e,xo,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")Vs(l)?(t=zl(l,t),e.tag=1,e=qh(null,e,l,t,n)):(e.tag=0,e=ss(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===Ms){e.tag=11,e=Lh(null,e,l,t,n);break t}else if(i===Ds){e.tag=14,e=Uh(null,e,l,t,n);break t}}throw e=Hc(l)||l,Error(M(306,e,""))}}return e;case 0:return ss(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=zl(l,e.pendingProps),qh(t,e,l,i,n);case 3:t:{if(fo(e,e.stateNode.containerInfo),t===null)throw Error(M(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,ns(t,e),zr(e,l,null,n);var a=e.memoizedState;if(l=a.cache,Bn(e,Ht,l),l!==r.cache&&$c(e,[Ht],n,!0),Ar(),l=a.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:a.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=jh(t,e,l,n);break t}else if(l!==i){i=_e(Error(M(424)),e),Hr(i),e=jh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(At=Ve(t.firstChild),oe=e,at=!0,vl=null,Je=!0,n=Gg(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ta(),l===i){e=wn(t,e,n);break t}Jt(t,e,l,n)}e=e.child}return e;case 26:return ro(t,e),t===null?(n=ad(e.type,null,e.pendingProps,null))?e.memoizedState=n:at||(n=e.type,t=e.pendingProps,l=Lo(Qn.current).createElement(n),l[te]=e,l[he]=t,Pt(l,n,t),Xt(l),e.stateNode=l):e.memoizedState=ad(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return jc(e),t===null&&at&&(l=e.stateNode=qy(e.type,e.pendingProps,Qn.current),oe=e,Je=!0,i=At,ll(e.type)?(Ts=i,At=Ve(l.firstChild)):At=i),Jt(t,e,e.pendingProps.children,n),ro(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&at&&((i=l=At)&&(l=QS(l,e.type,e.pendingProps,Je),l!==null?(e.stateNode=l,oe=e,At=Ve(l.firstChild),Je=!1,i=!0):i=!1),i||El(e)),jc(e),i=e.type,r=e.pendingProps,a=t!==null?t.memoizedProps:null,l=r.children,ks(i,r)?l=null:a!==null&&ks(i,a)&&(e.flags|=32),e.memoizedState!==null&&(i=Ws(t,e,fS,null,null,n),Xr._currentValue=i),ro(t,e),Jt(t,e,l,n),e.child;case 6:return t===null&&at&&((t=n=At)&&(n=ZS(n,e.pendingProps,Je),n!==null?(e.stateNode=n,oe=e,At=null,t=!0):t=!1),t||El(e)),null;case 13:return Wg(t,e,n);case 4:return fo(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Di(e,null,l,n):Jt(t,e,l,n),e.child;case 11:return Lh(t,e,e.type,e.pendingProps,n);case 7:return Jt(t,e,e.pendingProps,n),e.child;case 8:return Jt(t,e,e.pendingProps.children,n),e.child;case 12:return Jt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,Bn(e,e.type,l.value),Jt(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,Tl(e),i=ee(i),l=l(i),e.flags|=1,Jt(t,e,l,n),e.child;case 14:return Uh(t,e,e.type,e.pendingProps,n);case 15:return Kg(t,e,e.type,e.pendingProps,n);case 19:return Pg(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Co(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=xn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Jg(t,e,n);case 24:return Tl(e),l=ee(Ht),t===null?(i=Is(),i===null&&(i=dt,r=Zs(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Fs(e),Bn(e,Ht,i)):(t.lanes&n&&(ns(t,e),zr(e,null,null,n),Ar()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),Bn(e,Ht,l)):(l=r.cache,Bn(e,Ht,l),l!==i.cache&&$c(e,[Ht],n,!0))),Jt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(M(156,e.tag))}function fn(t){t.flags|=4}function Yh(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!Yy(e)){if(e=Le.current,e!==null&&((nt&4194048)===nt?tn!==null:(nt&62914560)!==nt&&!(nt&536870912)||e!==tn))throw Er=es,ig;t.flags|=8192}}function Qa(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Td():536870912,t.lanes|=e,Oi|=e)}function mr(t,e){if(!at)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Tt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function vS(t,e,n){var l=e.pendingProps;switch(Qs(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Tt(e),null;case 1:return Tt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),vn(Ht),Ei(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(sr(e)?fn(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,bh())),Tt(e),null;case 26:return n=e.memoizedState,t===null?(fn(e),n!==null?(Tt(e),Yh(e,n)):(Tt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(fn(e),Tt(e),Yh(e,n)):(Tt(e),e.flags&=-16777217):(t.memoizedProps!==l&&fn(e),Tt(e),e.flags&=-16777217),null;case 27:mo(e),n=Qn.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(M(166));return Tt(e),null}t=Pe.current,sr(e)?gh(e,t):(t=qy(i,l,n),e.stateNode=t,fn(e))}return Tt(e),null;case 5:if(mo(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(M(166));return Tt(e),null}if(t=Pe.current,sr(e))gh(e,t);else{switch(i=Lo(Qn.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[te]=e,t[he]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(Pt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fn(e)}}return Tt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(M(166));if(t=Qn.current,sr(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=oe,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[te]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Uy(t.nodeValue,n)),t||El(e)}else t=Lo(t).createTextNode(l),t[te]=e,e.stateNode=t}return Tt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=sr(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(M(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(M(317));i[te]=e}else ta(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;Tt(e),i=!1}else i=bh(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(bn(e),e):(bn(e),null)}if(bn(e),e.flags&128)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Qa(e,e.updateQueue),Tt(e),null;case 4:return Ei(),t===null&&yf(e.stateNode.containerInfo),Tt(e),null;case 10:return vn(e.type),Tt(e),null;case 19:if(Zt(qt),i=e.memoizedState,i===null)return Tt(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)mr(i,!1);else{if(zt!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(r=To(t),r!==null){for(e.flags|=128,mr(i,!1),t=r.updateQueue,e.updateQueue=t,Qa(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)tg(n,t),n=n.sibling;return kt(qt,qt.current&1|2),e.child}t=t.sibling}i.tail!==null&&$e()>Do&&(e.flags|=128,l=!0,mr(i,!1),e.lanes=4194304)}else{if(!l)if(t=To(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Qa(e,t),mr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!at)return Tt(e),null}else 2*$e()-i.renderingStartTime>Do&&n!==536870912&&(e.flags|=128,l=!0,mr(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=$e(),e.sibling=null,t=qt.current,kt(qt,l?t&1|2:t&1),e):(Tt(e),null);case 22:case 23:return bn(e),Ks(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?n&536870912&&!(e.flags&128)&&(Tt(e),e.subtreeFlags&6&&(e.flags|=8192)):Tt(e),n=e.updateQueue,n!==null&&Qa(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Zt(Sl),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),vn(Ht),Tt(e),null;case 25:return null;case 30:return null}throw Error(M(156,e.tag))}function SS(t,e){switch(Qs(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return vn(Ht),Ei(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return mo(e),null;case 13:if(bn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(M(340));ta()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Zt(qt),null;case 4:return Ei(),null;case 10:return vn(e.type),null;case 22:case 23:return bn(e),Ks(),t!==null&&Zt(Sl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return vn(Ht),null;case 25:return null;default:return null}}function ty(t,e){switch(Qs(e),e.tag){case 3:vn(Ht),Ei();break;case 26:case 27:case 5:mo(e);break;case 4:Ei();break;case 13:bn(e);break;case 19:Zt(qt);break;case 10:vn(e.type);break;case 22:case 23:bn(e),Ks(),t!==null&&Zt(Sl);break;case 24:vn(Ht)}}function aa(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,a=n.inst;l=r(),a.destroy=l}n=n.next}while(n!==i)}}catch(o){ht(e,e.return,o)}}function tl(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var a=l.inst,o=a.destroy;if(o!==void 0){a.destroy=void 0,i=e;var u=n,c=o;try{c()}catch(s){ht(i,u,s)}}}l=l.next}while(l!==r)}}catch(s){ht(e,e.return,s)}}function ey(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{og(e,n)}catch(l){ht(t,t.return,l)}}}function ny(t,e,n){n.props=zl(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){ht(t,e,l)}}function Mr(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){ht(t,e,i)}}function We(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){ht(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){ht(t,e,i)}else n.current=null}function ly(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){ht(t,t.return,i)}}function Ec(t,e,n){try{var l=t.stateNode;jS(l,t.type,n,e),l[he]=e}catch(i){ht(t,t.return,i)}}function iy(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ll(t.type)||t.tag===4}function Tc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||iy(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ll(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ms(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=tu));else if(l!==4&&(l===27&&ll(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function Mo(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&ll(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Mo(t,e,n),t=t.sibling;t!==null;)Mo(t,e,n),t=t.sibling}function ry(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);Pt(e,l,n),e[te]=t,e[he]=n}catch(r){ht(t,t.return,r)}}var pn=!1,Rt=!1,Ac=!1,Vh=typeof WeakSet=="function"?WeakSet:Set,Vt=null;function kS(t,e){if(t=t.containerInfo,vs=qo,t=Zd(t),js(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch(v){n=null;break t}var a=0,o=-1,u=-1,c=0,s=0,f=t,p=null;e:for(;;){for(var m;f!==n||i!==0&&f.nodeType!==3||(o=a+i),f!==r||l!==0&&f.nodeType!==3||(u=a+l),f.nodeType===3&&(a+=f.nodeValue.length),(m=f.firstChild)!==null;)p=f,f=m;for(;;){if(f===t)break e;if(p===n&&++c===i&&(o=a),p===r&&++s===l&&(u=a),(m=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=m}n=o===-1||u===-1?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ss={focusedElem:t,selectionRange:n},qo=!1,Vt=e;Vt!==null;)if(e=Vt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Vt=t;else for(;Vt!==null;){switch(e=Vt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var y=zl(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(y,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(v){ht(n,n.return,v)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ws(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ws(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(M(163))}if(t=e.sibling,t!==null){t.return=e.return,Vt=t;break}Vt=e.return}}function ay(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:_n(t,n),l&4&&aa(5,n);break;case 1:if(_n(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(a){ht(n,n.return,a)}else{var i=zl(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(a){ht(n,n.return,a)}}l&64&&ey(n),l&512&&Mr(n,n.return);break;case 3:if(_n(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{og(t,e)}catch(a){ht(n,n.return,a)}}break;case 27:e===null&&l&4&&ry(n);case 26:case 5:_n(t,n),e===null&&l&4&&ly(n),l&512&&Mr(n,n.return);break;case 12:_n(t,n);break;case 13:_n(t,n),l&4&&cy(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=OS.bind(null,n),IS(t,n))));break;case 22:if(l=n.memoizedState!==null||pn,!l){e=e!==null&&e.memoizedState!==null||Rt,i=pn;var r=Rt;pn=l,(Rt=e)&&!r?Nn(t,n,(n.subtreeFlags&8772)!==0):_n(t,n),pn=i,Rt=r}break;case 30:break;default:_n(t,n)}}function oy(t){var e=t.alternate;e!==null&&(t.alternate=null,oy(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ns(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var St=null,me=!1;function mn(t,e,n){for(n=n.child;n!==null;)uy(t,e,n),n=n.sibling}function uy(t,e,n){if(ke&&typeof ke.onCommitFiberUnmount=="function")try{ke.onCommitFiberUnmount(Kr,n)}catch(r){}switch(n.tag){case 26:Rt||We(n,e),mn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Rt||We(n,e);var l=St,i=me;ll(n.type)&&(St=n.stateNode,me=!1),mn(t,e,n),_r(n.stateNode),St=l,me=i;break;case 5:Rt||We(n,e);case 6:if(l=St,i=me,St=null,mn(t,e,n),St=l,me=i,St!==null)if(me)try{(St.nodeType===9?St.body:St.nodeName==="HTML"?St.ownerDocument.body:St).removeChild(n.stateNode)}catch(r){ht(n,e,r)}else try{St.removeChild(n.stateNode)}catch(r){ht(n,e,r)}break;case 18:St!==null&&(me?(t=St,ld(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Ir(t)):ld(St,n.stateNode));break;case 4:l=St,i=me,St=n.stateNode.containerInfo,me=!0,mn(t,e,n),St=l,me=i;break;case 0:case 11:case 14:case 15:Rt||tl(2,n,e),Rt||tl(4,n,e),mn(t,e,n);break;case 1:Rt||(We(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&ny(n,e,l)),mn(t,e,n);break;case 21:mn(t,e,n);break;case 22:Rt=(l=Rt)||n.memoizedState!==null,mn(t,e,n),Rt=l;break;default:mn(t,e,n)}}function cy(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ir(t)}catch(n){ht(e,e.return,n)}}function wS(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Vh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Vh),e;default:throw Error(M(435,t.tag))}}function zc(t,e){var n=wS(t);e.forEach(function(l){var i=RS.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function be(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,a=e,o=a;t:for(;o!==null;){switch(o.tag){case 27:if(ll(o.type)){St=o.stateNode,me=!1;break t}break;case 5:St=o.stateNode,me=!1;break t;case 3:case 4:St=o.stateNode.containerInfo,me=!0;break t}o=o.return}if(St===null)throw Error(M(160));uy(r,a,i),St=null,me=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)sy(e,t),e=e.sibling}var Ye=null;function sy(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:be(e,t),xe(t),l&4&&(tl(3,t,t.return),aa(3,t),tl(5,t,t.return));break;case 1:be(e,t),xe(t),l&512&&(Rt||n===null||We(n,n.return)),l&64&&pn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Ye;if(be(e,t),xe(t),l&512&&(Rt||n===null||We(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Pr]||r[te]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),Pt(r,l,n),r[te]=t,Xt(r),l=r;break t;case"link":var a=ud("link","href",i).get(l+(n.href||""));if(a){for(var o=0;o<a.length;o++)if(r=a[o],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){a.splice(o,1);break e}}r=i.createElement(l),Pt(r,l,n),i.head.appendChild(r);break;case"meta":if(a=ud("meta","content",i).get(l+(n.content||""))){for(o=0;o<a.length;o++)if(r=a[o],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){a.splice(o,1);break e}}r=i.createElement(l),Pt(r,l,n),i.head.appendChild(r);break;default:throw Error(M(468,l))}r[te]=t,Xt(r),l=r}t.stateNode=l}else cd(i,t.type,t.stateNode);else t.stateNode=od(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?cd(i,t.type,t.stateNode):od(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Ec(t,t.memoizedProps,n.memoizedProps)}break;case 27:be(e,t),xe(t),l&512&&(Rt||n===null||We(n,n.return)),n!==null&&l&4&&Ec(t,t.memoizedProps,n.memoizedProps);break;case 5:if(be(e,t),xe(t),l&512&&(Rt||n===null||We(n,n.return)),t.flags&32){i=t.stateNode;try{Ai(i,"")}catch(m){ht(t,t.return,m)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,Ec(t,i,n!==null?n.memoizedProps:i)),l&1024&&(Ac=!0);break;case 6:if(be(e,t),xe(t),l&4){if(t.stateNode===null)throw Error(M(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(m){ht(t,t.return,m)}}break;case 3:if(uo=null,i=Ye,Ye=Uo(e.containerInfo),be(e,t),Ye=i,xe(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Ir(e.containerInfo)}catch(m){ht(t,t.return,m)}Ac&&(Ac=!1,fy(t));break;case 4:l=Ye,Ye=Uo(t.stateNode.containerInfo),be(e,t),xe(t),Ye=l;break;case 12:be(e,t),xe(t);break;case 13:be(e,t),xe(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(hf=$e()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,zc(t,l)));break;case 22:i=t.memoizedState!==null;var u=n!==null&&n.memoizedState!==null,c=pn,s=Rt;if(pn=c||i,Rt=s||u,be(e,t),Rt=s,pn=c,xe(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||u||pn||Rt||gl(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){u=n=e;try{if(r=u.stateNode,i)a=r.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none";else{o=u.stateNode;var f=u.memoizedProps.style,p=f!=null&&f.hasOwnProperty("display")?f.display:null;o.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(m){ht(u,u.return,m)}}}else if(e.tag===6){if(n===null){u=e;try{u.stateNode.nodeValue=i?"":u.memoizedProps}catch(m){ht(u,u.return,m)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,zc(t,n))));break;case 19:be(e,t),xe(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,zc(t,l)));break;case 30:break;case 21:break;default:be(e,t),xe(t)}}function xe(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(iy(l)){n=l;break}l=l.return}if(n==null)throw Error(M(160));switch(n.tag){case 27:var i=n.stateNode,r=Tc(t);Mo(t,r,i);break;case 5:var a=n.stateNode;n.flags&32&&(Ai(a,""),n.flags&=-33);var o=Tc(t);Mo(t,o,a);break;case 3:case 4:var u=n.stateNode.containerInfo,c=Tc(t);ms(t,c,u);break;default:throw Error(M(161))}}catch(s){ht(t,t.return,s)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function fy(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;fy(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function _n(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)ay(t,e.alternate,e),e=e.sibling}function gl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:tl(4,e,e.return),gl(e);break;case 1:We(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&ny(e,e.return,n),gl(e);break;case 27:_r(e.stateNode);case 26:case 5:We(e,e.return),gl(e);break;case 22:e.memoizedState===null&&gl(e);break;case 30:gl(e);break;default:gl(e)}t=t.sibling}}function Nn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,a=r.flags;switch(r.tag){case 0:case 11:case 15:Nn(i,r,n),aa(4,r);break;case 1:if(Nn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(c){ht(l,l.return,c)}if(l=r,i=l.updateQueue,i!==null){var o=l.stateNode;try{var u=i.shared.hiddenCallbacks;if(u!==null)for(i.shared.hiddenCallbacks=null,i=0;i<u.length;i++)ag(u[i],o)}catch(c){ht(l,l.return,c)}}n&&a&64&&ey(r),Mr(r,r.return);break;case 27:ry(r);case 26:case 5:Nn(i,r,n),n&&l===null&&a&4&&ly(r),Mr(r,r.return);break;case 12:Nn(i,r,n);break;case 13:Nn(i,r,n),n&&a&4&&cy(i,r);break;case 22:r.memoizedState===null&&Nn(i,r,n),Mr(r,r.return);break;case 30:break;default:Nn(i,r,n)}e=e.sibling}}function sf(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&na(n))}function ff(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&na(t))}function Ke(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)my(t,e,n,l),e=e.sibling}function my(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Ke(t,e,n,l),i&2048&&aa(9,e);break;case 1:Ke(t,e,n,l);break;case 3:Ke(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&na(t)));break;case 12:if(i&2048){Ke(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,a=r.id,o=r.onPostCommit;typeof o=="function"&&o(a,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(u){ht(e,e.return,u)}}else Ke(t,e,n,l);break;case 13:Ke(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,a=e.alternate,e.memoizedState!==null?r._visibility&2?Ke(t,e,n,l):Dr(t,e):r._visibility&2?Ke(t,e,n,l):(r._visibility|=2,ni(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&sf(a,e);break;case 24:Ke(t,e,n,l),i&2048&&ff(e.alternate,e);break;default:Ke(t,e,n,l)}}function ni(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,a=e,o=n,u=l,c=a.flags;switch(a.tag){case 0:case 11:case 15:ni(r,a,o,u,i),aa(8,a);break;case 23:break;case 22:var s=a.stateNode;a.memoizedState!==null?s._visibility&2?ni(r,a,o,u,i):Dr(r,a):(s._visibility|=2,ni(r,a,o,u,i)),i&&c&2048&&sf(a.alternate,a);break;case 24:ni(r,a,o,u,i),i&&c&2048&&ff(a.alternate,a);break;default:ni(r,a,o,u,i)}e=e.sibling}}function Dr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:Dr(n,l),i&2048&&sf(l.alternate,l);break;case 24:Dr(n,l),i&2048&&ff(l.alternate,l);break;default:Dr(n,l)}e=e.sibling}}var xr=8192;function $l(t){if(t.subtreeFlags&xr)for(t=t.child;t!==null;)py(t),t=t.sibling}function py(t){switch(t.tag){case 26:$l(t),t.flags&xr&&t.memoizedState!==null&&ak(Ye,t.memoizedState,t.memoizedProps);break;case 5:$l(t);break;case 3:case 4:var e=Ye;Ye=Uo(t.stateNode.containerInfo),$l(t),Ye=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=xr,xr=16777216,$l(t),xr=e):$l(t));break;default:$l(t)}}function hy(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function pr(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Vt=l,gy(l,t)}hy(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)dy(t),t=t.sibling}function dy(t){switch(t.tag){case 0:case 11:case 15:pr(t),t.flags&2048&&tl(9,t,t.return);break;case 3:pr(t);break;case 12:pr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ao(t)):pr(t);break;default:pr(t)}}function ao(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Vt=l,gy(l,t)}hy(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:tl(8,e,e.return),ao(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ao(e));break;default:ao(e)}t=t.sibling}}function gy(t,e){for(;Vt!==null;){var n=Vt;switch(n.tag){case 0:case 11:case 15:tl(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:na(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Vt=l;else t:for(n=t;Vt!==null;){l=Vt;var i=l.sibling,r=l.return;if(oy(l),l===n){Vt=null;break t}if(i!==null){i.return=r,Vt=i;break t}Vt=r}}}var ES={getCacheForType:function(t){var e=ee(Ht),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},TS=typeof WeakMap=="function"?WeakMap:Map,ct=0,dt=null,tt=null,nt=0,ut=0,ve=null,Vn=!1,Hi=!1,mf=!1,En=0,zt=0,el=0,kl=0,pf=0,Ne=0,Oi=0,Or=null,pe=null,ps=!1,hf=0,Do=1/0,Oo=null,Fn=null,Wt=0,Kn=null,Ri=null,wi=0,hs=0,ds=null,yy=null,Rr=0,gs=null;function Ee(){if(ct&2&&nt!==0)return nt&-nt;if(Q.T!==null){var t=zi;return t!==0?t:gf()}return Cd()}function by(){Ne===0&&(Ne=!(nt&536870912)||at?Ed():536870912);var t=Le.current;return t!==null&&(t.flags|=32),Ne}function Te(t,e,n){(t===dt&&(ut===2||ut===9)||t.cancelPendingCommit!==null)&&(_i(t,0),Xn(t,nt,Ne,!1)),Wr(t,n),(!(ct&2)||t!==dt)&&(t===dt&&(!(ct&2)&&(kl|=n),zt===4&&Xn(t,nt,Ne,!1)),nn(t))}function xy(t,e,n){if(ct&6)throw Error(M(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Jr(t,e),i=l?CS(t,e):Cc(t,e,!0),r=l;do{if(i===0){Hi&&!l&&Xn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!AS(n)){i=Cc(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var a=0;else a=t.pendingLanes&-536870913,a=a!==0?a:a&536870912?536870912:0;if(a!==0){e=a;t:{var o=t;i=Or;var u=o.current.memoizedState.isDehydrated;if(u&&(_i(o,a).flags|=256),a=Cc(o,a,!1),a!==2){if(mf&&!u){o.errorRecoveryDisabledLanes|=r,kl|=r,i=4;break t}r=pe,pe=i,r!==null&&(pe===null?pe=r:pe.push.apply(pe,r))}i=a}if(r=!1,i!==2)continue}}if(i===1){_i(t,0),Xn(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(M(345));case 4:if((e&4194048)!==e)break;case 6:Xn(l,e,Ne,!Vn);break t;case 2:pe=null;break;case 3:case 5:break;default:throw Error(M(329))}if((e&62914560)===e&&(i=hf+300-$e(),10<i)){if(Xn(l,e,Ne,!Vn),Go(l,0,!0)!==0)break t;l.timeoutHandle=Hy(Xh.bind(null,l,n,pe,Oo,ps,e,Ne,kl,Oi,Vn,r,2,-0,0),i);break t}Xh(l,n,pe,Oo,ps,e,Ne,kl,Oi,Vn,r,0,-0,0)}}break}while(!0);nn(t)}function Xh(t,e,n,l,i,r,a,o,u,c,s,f,p,m){if(t.timeoutHandle=-1,f=e.subtreeFlags,(f&8192||(f&16785408)===16785408)&&(Vr={stylesheets:null,count:0,unsuspend:rk},py(e),f=ok(),f!==null)){t.cancelPendingCommit=f(Zh.bind(null,t,e,r,n,l,i,a,o,u,s,1,p,m)),Xn(t,r,a,!c);return}Zh(t,e,r,n,l,i,a,o,u)}function AS(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!Ae(r(),i))return!1}catch(a){return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Xn(t,e,n,l){e&=~pf,e&=~kl,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-we(i),a=1<<r;l[r]=-1,i&=~a}n!==0&&Ad(t,n,e)}function Wo(){return ct&6?!0:(oa(0,!1),!1)}function df(){if(tt!==null){if(ut===0)var t=tt.return;else t=tt,yn=Ol=null,tf(t),ki=null,jr=0,t=tt;for(;t!==null;)ty(t.alternate,t),t=t.return;tt=null}}function _i(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,YS(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),df(),dt=t,tt=n=xn(t.current,null),nt=e,ut=0,ve=null,Vn=!1,Hi=Jr(t,e),mf=!1,Oi=Ne=pf=kl=el=zt=0,pe=Or=null,ps=!1,e&8&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-we(l),r=1<<i;e|=t[i],l&=~r}return En=e,Qo(),n}function vy(t,e){J=null,Q.H=Eo,e===la||e===Io?(e=kh(),ut=3):e===ig?(e=kh(),ut=4):ut=e===Fg?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ve=e,tt===null&&(zt=1,zo(t,_e(e,t.current)))}function Sy(){var t=Q.H;return Q.H=Eo,t===null?Eo:t}function ky(){var t=Q.A;return Q.A=ES,t}function ys(){zt=4,Vn||(nt&4194048)!==nt&&Le.current!==null||(Hi=!0),!(el&134217727)&&!(kl&134217727)||dt===null||Xn(dt,nt,Ne,!1)}function Cc(t,e,n){var l=ct;ct|=2;var i=Sy(),r=ky();(dt!==t||nt!==e)&&(Oo=null,_i(t,e)),e=!1;var a=zt;t:do try{if(ut!==0&&tt!==null){var o=tt,u=ve;switch(ut){case 8:df(),a=6;break t;case 3:case 2:case 9:case 6:Le.current===null&&(e=!0);var c=ut;if(ut=0,ve=null,di(t,o,u,c),n&&Hi){a=0;break t}break;default:c=ut,ut=0,ve=null,di(t,o,u,c)}}zS(),a=zt;break}catch(s){vy(t,s)}while(!0);return e&&t.shellSuspendCounter++,yn=Ol=null,ct=l,Q.H=i,Q.A=r,tt===null&&(dt=null,nt=0,Qo()),a}function zS(){for(;tt!==null;)wy(tt)}function CS(t,e){var n=ct;ct|=2;var l=Sy(),i=ky();dt!==t||nt!==e?(Oo=null,Do=$e()+500,_i(t,e)):Hi=Jr(t,e);t:do try{if(ut!==0&&tt!==null){e=tt;var r=ve;e:switch(ut){case 1:ut=0,ve=null,di(t,e,r,1);break;case 2:case 9:if(Sh(r)){ut=0,ve=null,Qh(e);break}e=function(){ut!==2&&ut!==9||dt!==t||(ut=7),nn(t)},r.then(e,e);break t;case 3:ut=7;break t;case 4:ut=5;break t;case 7:Sh(r)?(ut=0,ve=null,Qh(e)):(ut=0,ve=null,di(t,e,r,7));break;case 5:var a=null;switch(tt.tag){case 26:a=tt.memoizedState;case 5:case 27:var o=tt;if(!a||Yy(a)){ut=0,ve=null;var u=o.sibling;if(u!==null)tt=u;else{var c=o.return;c!==null?(tt=c,Po(c)):tt=null}break e}}ut=0,ve=null,di(t,e,r,5);break;case 6:ut=0,ve=null,di(t,e,r,6);break;case 8:df(),zt=6;break t;default:throw Error(M(462))}}MS();break}catch(s){vy(t,s)}while(!0);return yn=Ol=null,Q.H=l,Q.A=i,ct=n,tt!==null?0:(dt=null,nt=0,Qo(),zt)}function MS(){for(;tt!==null&&!Wx();)wy(tt)}function wy(t){var e=$g(t.alternate,t,En);t.memoizedProps=t.pendingProps,e===null?Po(t):tt=e}function Qh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Hh(n,e,e.pendingProps,e.type,void 0,nt);break;case 11:e=Hh(n,e,e.pendingProps,e.type.render,e.ref,nt);break;case 5:tf(e);default:ty(n,e),e=tt=tg(e,En),e=$g(n,e,En)}t.memoizedProps=t.pendingProps,e===null?Po(t):tt=e}function di(t,e,n,l){yn=Ol=null,tf(e),ki=null,jr=0;var i=e.return;try{if(bS(t,i,e,n,nt)){zt=1,zo(t,_e(n,t.current)),tt=null;return}}catch(r){if(i!==null)throw tt=i,r;zt=1,zo(t,_e(n,t.current)),tt=null;return}e.flags&32768?(at||l===1?t=!0:Hi||nt&536870912?t=!1:(Vn=t=!0,(l===2||l===9||l===3||l===6)&&(l=Le.current,l!==null&&l.tag===13&&(l.flags|=16384))),Ey(e,t)):Po(e)}function Po(t){var e=t;do{if(e.flags&32768){Ey(e,Vn);return}t=e.return;var n=vS(e.alternate,e,En);if(n!==null){tt=n;return}if(e=e.sibling,e!==null){tt=e;return}tt=e=t}while(e!==null);zt===0&&(zt=5)}function Ey(t,e){do{var n=SS(t.alternate,t);if(n!==null){n.flags&=32767,tt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){tt=t;return}tt=t=n}while(t!==null);zt=6,tt=null}function Zh(t,e,n,l,i,r,a,o,u){t.cancelPendingCommit=null;do $o();while(Wt!==0);if(ct&6)throw Error(M(327));if(e!==null){if(e===t.current)throw Error(M(177));if(r=e.lanes|e.childLanes,r|=Gs,ov(t,n,r,a,o,u),t===dt&&(tt=dt=null,nt=0),Ri=e,Kn=t,wi=n,hs=r,ds=i,yy=l,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,_S(po,function(){return My(!0),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,e.subtreeFlags&13878||l){l=Q.T,Q.T=null,i=ot.p,ot.p=2,a=ct,ct|=4;try{kS(t,e,n)}finally{ct=a,ot.p=i,Q.T=l}}Wt=1,Ty(),Ay(),zy()}}function Ty(){if(Wt===1){Wt=0;var t=Kn,e=Ri,n=(e.flags&13878)!==0;if(e.subtreeFlags&13878||n){n=Q.T,Q.T=null;var l=ot.p;ot.p=2;var i=ct;ct|=4;try{sy(e,t);var r=Ss,a=Zd(t.containerInfo),o=r.focusedElem,u=r.selectionRange;if(a!==o&&o&&o.ownerDocument&&Qd(o.ownerDocument.documentElement,o)){if(u!==null&&js(o)){var c=u.start,s=u.end;if(s===void 0&&(s=c),"selectionStart"in o)o.selectionStart=c,o.selectionEnd=Math.min(s,o.value.length);else{var f=o.ownerDocument||document,p=f&&f.defaultView||window;if(p.getSelection){var m=p.getSelection(),y=o.textContent.length,v=Math.min(u.start,y),E=u.end===void 0?v:Math.min(u.end,y);!m.extend&&v>E&&(a=E,E=v,v=a);var h=ph(o,v),d=ph(o,E);if(h&&d&&(m.rangeCount!==1||m.anchorNode!==h.node||m.anchorOffset!==h.offset||m.focusNode!==d.node||m.focusOffset!==d.offset)){var g=f.createRange();g.setStart(h.node,h.offset),m.removeAllRanges(),v>E?(m.addRange(g),m.extend(d.node,d.offset)):(g.setEnd(d.node,d.offset),m.addRange(g))}}}}for(f=[],m=o;m=m.parentNode;)m.nodeType===1&&f.push({element:m,left:m.scrollLeft,top:m.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<f.length;o++){var S=f[o];S.element.scrollLeft=S.left,S.element.scrollTop=S.top}}qo=!!vs,Ss=vs=null}finally{ct=i,ot.p=l,Q.T=n}}t.current=e,Wt=2}}function Ay(){if(Wt===2){Wt=0;var t=Kn,e=Ri,n=(e.flags&8772)!==0;if(e.subtreeFlags&8772||n){n=Q.T,Q.T=null;var l=ot.p;ot.p=2;var i=ct;ct|=4;try{ay(t,e.alternate,e)}finally{ct=i,ot.p=l,Q.T=n}}Wt=3}}function zy(){if(Wt===4||Wt===3){Wt=0,Px();var t=Kn,e=Ri,n=wi,l=yy;e.subtreeFlags&10256||e.flags&10256?Wt=5:(Wt=0,Ri=Kn=null,Cy(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(Fn=null),_s(n),e=e.stateNode,ke&&typeof ke.onCommitFiberRoot=="function")try{ke.onCommitFiberRoot(Kr,e,void 0,(e.current.flags&128)===128)}catch(u){}if(l!==null){e=Q.T,i=ot.p,ot.p=2,Q.T=null;try{for(var r=t.onRecoverableError,a=0;a<l.length;a++){var o=l[a];r(o.value,{componentStack:o.stack})}}finally{Q.T=e,ot.p=i}}wi&3&&$o(),nn(t),i=t.pendingLanes,n&4194090&&i&42?t===gs?Rr++:(Rr=0,gs=t):Rr=0,oa(0,!1)}}function Cy(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,na(e)))}function $o(t){return Ty(),Ay(),zy(),My(t)}function My(){if(Wt!==5)return!1;var t=Kn,e=hs;hs=0;var n=_s(wi),l=Q.T,i=ot.p;try{ot.p=32>n?32:n,Q.T=null,n=ds,ds=null;var r=Kn,a=wi;if(Wt=0,Ri=Kn=null,wi=0,ct&6)throw Error(M(331));var o=ct;if(ct|=4,dy(r.current),my(r,r.current,a,n),ct=o,oa(0,!1),ke&&typeof ke.onPostCommitFiberRoot=="function")try{ke.onPostCommitFiberRoot(Kr,r)}catch(u){}return!0}finally{ot.p=i,Q.T=l,Cy(t,e)}}function Ih(t,e,n){e=_e(n,e),e=cs(t.stateNode,e,2),t=In(t,e,2),t!==null&&(Wr(t,2),nn(t))}function ht(t,e,n){if(t.tag===3)Ih(t,t,n);else for(;e!==null;){if(e.tag===3){Ih(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Fn===null||!Fn.has(l))){t=_e(n,t),n=Zg(2),l=In(e,n,2),l!==null&&(Ig(n,l,e,t),Wr(l,2),nn(l));break}}e=e.return}}function Mc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new TS;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(mf=!0,i.add(n),t=DS.bind(null,t,e,n),e.then(t,t))}function DS(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,dt===t&&(nt&n)===n&&(zt===4||zt===3&&(nt&62914560)===nt&&300>$e()-hf?!(ct&2)&&_i(t,0):pf|=n,Oi===nt&&(Oi=0)),nn(t)}function Dy(t,e){e===0&&(e=Td()),t=Bi(t,e),t!==null&&(Wr(t,e),nn(t))}function OS(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Dy(t,n)}function RS(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(M(314))}l!==null&&l.delete(e),Dy(t,n)}function _S(t,e){return Os(t,e)}var Ro=null,li=null,bs=!1,_o=!1,Dc=!1,wl=0;function nn(t){t!==li&&t.next===null&&(li===null?Ro=li=t:li=li.next=t),_o=!0,bs||(bs=!0,LS())}function oa(t,e){if(!Dc&&_o){Dc=!0;do for(var n=!1,l=Ro;l!==null;){if(!e)if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var a=l.suspendedLanes,o=l.pingedLanes;r=(1<<31-we(42|t)+1)-1,r&=i&~(a&~o),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Fh(l,r))}else r=nt,r=Go(l,l===dt?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),!(r&3)||Jr(l,r)||(n=!0,Fh(l,r));l=l.next}while(n);Dc=!1}}function NS(){Oy()}function Oy(){_o=bs=!1;var t=0;wl!==0&&(GS()&&(t=wl),wl=0);for(var e=$e(),n=null,l=Ro;l!==null;){var i=l.next,r=Ry(l,e);r===0?(l.next=null,n===null?Ro=i:n.next=i,i===null&&(li=n)):(n=l,(t!==0||r&3)&&(_o=!0)),l=i}oa(t,!1)}function Ry(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var a=31-we(r),o=1<<a,u=i[a];u===-1?(!(o&n)||o&l)&&(i[a]=av(o,e)):u<=e&&(t.expiredLanes|=o),r&=~o}if(e=dt,n=nt,n=Go(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(ut===2||ut===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&lc(l),t.callbackNode=null,t.callbackPriority=0;if(!(n&3)||Jr(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&lc(l),_s(n)){case 2:case 8:n=kd;break;case 32:n=po;break;case 268435456:n=wd;break;default:n=po}return l=_y.bind(null,t),n=Os(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&lc(l),t.callbackPriority=2,t.callbackNode=null,2}function _y(t,e){if(Wt!==0&&Wt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if($o(!0)&&t.callbackNode!==n)return null;var l=nt;return l=Go(t,t===dt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(xy(t,l,e),Ry(t,$e()),t.callbackNode!=null&&t.callbackNode===n?_y.bind(null,t):null)}function Fh(t,e){if($o())return null;xy(t,e,!0)}function LS(){VS(function(){ct&6?Os(Sd,NS):Oy()})}function gf(){return wl===0&&(wl=Ed()),wl}function Kh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Pa(""+t)}function Jh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function US(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=Kh((i[he]||null).action),a=l.submitter;a&&(e=(e=a[he]||null)?Kh(e.formAction):a.getAttribute("formAction"),e!==null&&(r=e,a=null));var o=new Yo("action","action",null,l,i);t.push({event:o,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(wl!==0){var u=a?Jh(i,a):new FormData(i);os(n,{pending:!0,data:u,method:i.method,action:r},null,u)}}else typeof r=="function"&&(o.preventDefault(),u=a?Jh(i,a):new FormData(i),os(n,{pending:!0,data:u,method:i.method,action:r},r,u))},currentTarget:i}]})}}for(Za=0;Za<Kc.length;Za++)Ia=Kc[Za],Wh=Ia.toLowerCase(),Ph=Ia[0].toUpperCase()+Ia.slice(1),Xe(Wh,"on"+Ph);var Ia,Wh,Ph,Za;Xe(Fd,"onAnimationEnd");Xe(Kd,"onAnimationIteration");Xe(Jd,"onAnimationStart");Xe("dblclick","onDoubleClick");Xe("focusin","onFocus");Xe("focusout","onBlur");Xe(eS,"onTransitionRun");Xe(nS,"onTransitionStart");Xe(lS,"onTransitionCancel");Xe(Wd,"onTransitionEnd");Ti("onMouseEnter",["mouseout","mouseover"]);Ti("onMouseLeave",["mouseout","mouseover"]);Ti("onPointerEnter",["pointerout","pointerover"]);Ti("onPointerLeave",["pointerout","pointerover"]);Cl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Cl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Cl("onBeforeInput",["compositionend","keypress","textInput","paste"]);Cl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Cl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Cl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),BS=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Gr));function Ny(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var a=l.length-1;0<=a;a--){var o=l[a],u=o.instance,c=o.currentTarget;if(o=o.listener,u!==r&&i.isPropagationStopped())break t;r=o,i.currentTarget=c;try{r(i)}catch(s){Ao(s)}i.currentTarget=null,r=u}else for(a=0;a<l.length;a++){if(o=l[a],u=o.instance,c=o.currentTarget,o=o.listener,u!==r&&i.isPropagationStopped())break t;r=o,i.currentTarget=c;try{r(i)}catch(s){Ao(s)}i.currentTarget=null,r=u}}}}function $(t,e){var n=e[Yc];n===void 0&&(n=e[Yc]=new Set);var l=t+"__bubble";n.has(l)||(Ly(e,t,2,!1),n.add(l))}function Oc(t,e,n){var l=0;e&&(l|=4),Ly(n,t,l,e)}var Fa="_reactListening"+Math.random().toString(36).slice(2);function yf(t){if(!t[Fa]){t[Fa]=!0,Md.forEach(function(n){n!=="selectionchange"&&(BS.has(n)||Oc(n,!1,t),Oc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Fa]||(e[Fa]=!0,Oc("selectionchange",!1,e))}}function Ly(t,e,n,l){switch(Iy(e)){case 2:var i=sk;break;case 8:i=fk;break;default:i=Sf}n=i.bind(null,e,n,t),i=void 0,!Zc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Rc(t,e,n,l,i){var r=l;if(!(e&1)&&!(e&2)&&l!==null)t:for(;;){if(l===null)return;var a=l.tag;if(a===3||a===4){var o=l.stateNode.containerInfo;if(o===i)break;if(a===4)for(a=l.return;a!==null;){var u=a.tag;if((u===3||u===4)&&a.stateNode.containerInfo===i)return;a=a.return}for(;o!==null;){if(a=ai(o),a===null)return;if(u=a.tag,u===5||u===6||u===26||u===27){l=r=a;continue t}o=o.parentNode}}l=l.return}Bd(function(){var c=r,s=Us(n),f=[];t:{var p=Pd.get(t);if(p!==void 0){var m=Yo,y=t;switch(t){case"keypress":if(to(n)===0)break t;case"keydown":case"keyup":m=_v;break;case"focusin":y="focus",m=fc;break;case"focusout":y="blur",m=fc;break;case"beforeblur":case"afterblur":m=fc;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=ih;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Sv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Uv;break;case Fd:case Kd:case Jd:m=Ev;break;case Wd:m=Hv;break;case"scroll":case"scrollend":m=xv;break;case"wheel":m=jv;break;case"copy":case"cut":case"paste":m=Av;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=ah;break;case"toggle":case"beforetoggle":m=Yv}var v=(e&4)!==0,E=!v&&(t==="scroll"||t==="scrollend"),h=v?p!==null?p+"Capture":null:p;v=[];for(var d=c,g;d!==null;){var S=d;if(g=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||g===null||h===null||(S=Lr(d,h),S!=null&&v.push(Yr(d,S,g))),E)break;d=d.return}0<v.length&&(p=new m(p,y,null,n,s),f.push({event:p,listeners:v}))}}if(!(e&7)){t:{if(p=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",p&&n!==Qc&&(y=n.relatedTarget||n.fromElement)&&(ai(y)||y[Li]))break t;if((m||p)&&(p=s.window===s?s:(p=s.ownerDocument)?p.defaultView||p.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=c,y=y?ai(y):null,y!==null&&(E=Fr(y),v=y.tag,y!==E||v!==5&&v!==27&&v!==6)&&(y=null)):(m=null,y=c),m!==y)){if(v=ih,S="onMouseLeave",h="onMouseEnter",d="mouse",(t==="pointerout"||t==="pointerover")&&(v=ah,S="onPointerLeave",h="onPointerEnter",d="pointer"),E=m==null?p:br(m),g=y==null?p:br(y),p=new v(S,d+"leave",m,n,s),p.target=E,p.relatedTarget=g,S=null,ai(s)===c&&(v=new v(h,d+"enter",y,n,s),v.target=g,v.relatedTarget=E,S=v),E=S,m&&y)e:{for(v=m,h=y,d=0,g=v;g;g=ti(g))d++;for(g=0,S=h;S;S=ti(S))g++;for(;0<d-g;)v=ti(v),d--;for(;0<g-d;)h=ti(h),g--;for(;d--;){if(v===h||h!==null&&v===h.alternate)break e;v=ti(v),h=ti(h)}v=null}else v=null;m!==null&&$h(f,p,m,v,!1),y!==null&&E!==null&&$h(f,E,y,v,!0)}}t:{if(p=c?br(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var z=sh;else if(ch(p))if(Vd)z=Pv;else{z=Jv;var w=Kv}else m=p.nodeName,!m||m.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?c&&Ls(c.elementType)&&(z=sh):z=Wv;if(z&&(z=z(t,c))){Yd(f,z,n,s);break t}w&&w(t,p,c),t==="focusout"&&c&&p.type==="number"&&c.memoizedProps.value!=null&&Xc(p,"number",p.value)}switch(w=c?br(c):window,t){case"focusin":(ch(w)||w.contentEditable==="true")&&(ci=w,Ic=c,kr=null);break;case"focusout":kr=Ic=ci=null;break;case"mousedown":Fc=!0;break;case"contextmenu":case"mouseup":case"dragend":Fc=!1,hh(f,n,s);break;case"selectionchange":if(tS)break;case"keydown":case"keyup":hh(f,n,s)}var D;if(qs)t:{switch(t){case"compositionstart":var T="onCompositionStart";break t;case"compositionend":T="onCompositionEnd";break t;case"compositionupdate":T="onCompositionUpdate";break t}T=void 0}else ui?jd(t,n)&&(T="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(qd&&n.locale!=="ko"&&(ui||T!=="onCompositionStart"?T==="onCompositionEnd"&&ui&&(D=Hd()):(Yn=s,Bs="value"in Yn?Yn.value:Yn.textContent,ui=!0)),w=No(c,T),0<w.length&&(T=new rh(T,t,null,n,s),f.push({event:T,listeners:w}),D?T.data=D:(D=Gd(n),D!==null&&(T.data=D)))),(D=Xv?Qv(t,n):Zv(t,n))&&(T=No(c,"onBeforeInput"),0<T.length&&(w=new rh("onBeforeInput","beforeinput",null,n,s),f.push({event:w,listeners:T}),w.data=D)),US(f,t,c,n,s)}Ny(f,e)})}function Yr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function No(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=Lr(t,n),i!=null&&l.unshift(Yr(t,i,r)),i=Lr(t,e),i!=null&&l.push(Yr(t,i,r))),t.tag===3)return l;t=t.return}return[]}function ti(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function $h(t,e,n,l,i){for(var r=e._reactName,a=[];n!==null&&n!==l;){var o=n,u=o.alternate,c=o.stateNode;if(o=o.tag,u!==null&&u===l)break;o!==5&&o!==26&&o!==27||c===null||(u=c,i?(c=Lr(n,r),c!=null&&a.unshift(Yr(n,c,u))):i||(c=Lr(n,r),c!=null&&a.push(Yr(n,c,u)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var HS=/\r\n?/g,qS=/\u0000|\uFFFD/g;function td(t){return(typeof t=="string"?t:""+t).replace(HS,`
`).replace(qS,"")}function Uy(t,e){return e=td(e),td(t)===e}function tu(){}function ft(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Ai(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Ai(t,""+l);break;case"className":Ba(t,"class",l);break;case"tabIndex":Ba(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ba(t,n,l);break;case"style":Ud(t,l,r);break;case"data":if(e!=="object"){Ba(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Pa(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&ft(t,e,"name",i.name,i,null),ft(t,e,"formEncType",i.formEncType,i,null),ft(t,e,"formMethod",i.formMethod,i,null),ft(t,e,"formTarget",i.formTarget,i,null)):(ft(t,e,"encType",i.encType,i,null),ft(t,e,"method",i.method,i,null),ft(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Pa(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=tu);break;case"onScroll":l!=null&&$("scroll",t);break;case"onScrollEnd":l!=null&&$("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(M(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(M(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Pa(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":$("beforetoggle",t),$("toggle",t),Wa(t,"popover",l);break;case"xlinkActuate":sn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":sn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":sn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":sn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":sn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":sn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":sn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":sn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":sn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Wa(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=yv.get(n)||n,Wa(t,n,l))}}function xs(t,e,n,l,i,r){switch(n){case"style":Ud(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(M(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(M(60));t.innerHTML=n}}break;case"children":typeof l=="string"?Ai(t,l):(typeof l=="number"||typeof l=="bigint")&&Ai(t,""+l);break;case"onScroll":l!=null&&$("scroll",t);break;case"onScrollEnd":l!=null&&$("scrollend",t);break;case"onClick":l!=null&&(t.onclick=tu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Dd.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[he]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Wa(t,n,l)}}}function Pt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":$("error",t),$("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var a=n[r];if(a!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(M(137,e));default:ft(t,e,r,a,n,null)}}i&&ft(t,e,"srcSet",n.srcSet,n,null),l&&ft(t,e,"src",n.src,n,null);return;case"input":$("invalid",t);var o=r=a=i=null,u=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var s=n[l];if(s!=null)switch(l){case"name":i=s;break;case"type":a=s;break;case"checked":u=s;break;case"defaultChecked":c=s;break;case"value":r=s;break;case"defaultValue":o=s;break;case"children":case"dangerouslySetInnerHTML":if(s!=null)throw Error(M(137,e));break;default:ft(t,e,l,s,n,null)}}_d(t,r,o,u,c,a,i,!1),ho(t);return;case"select":$("invalid",t),l=a=r=null;for(i in n)if(n.hasOwnProperty(i)&&(o=n[i],o!=null))switch(i){case"value":r=o;break;case"defaultValue":a=o;break;case"multiple":l=o;default:ft(t,e,i,o,n,null)}e=r,n=a,t.multiple=!!l,e!=null?yi(t,!!l,e,!1):n!=null&&yi(t,!!l,n,!0);return;case"textarea":$("invalid",t),r=i=l=null;for(a in n)if(n.hasOwnProperty(a)&&(o=n[a],o!=null))switch(a){case"value":l=o;break;case"defaultValue":i=o;break;case"children":r=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(M(91));break;default:ft(t,e,a,o,n,null)}Ld(t,l,i,r),ho(t);return;case"option":for(u in n)if(n.hasOwnProperty(u)&&(l=n[u],l!=null))switch(u){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:ft(t,e,u,l,n,null)}return;case"dialog":$("beforetoggle",t),$("toggle",t),$("cancel",t),$("close",t);break;case"iframe":case"object":$("load",t);break;case"video":case"audio":for(l=0;l<Gr.length;l++)$(Gr[l],t);break;case"image":$("error",t),$("load",t);break;case"details":$("toggle",t);break;case"embed":case"source":case"link":$("error",t),$("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&(l=n[c],l!=null))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(M(137,e));default:ft(t,e,c,l,n,null)}return;default:if(Ls(e)){for(s in n)n.hasOwnProperty(s)&&(l=n[s],l!==void 0&&xs(t,e,s,l,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(l=n[o],l!=null&&ft(t,e,o,l,n,null))}function jS(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,a=null,o=null,u=null,c=null,s=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&f!=null)switch(m){case"checked":break;case"value":break;case"defaultValue":u=f;default:l.hasOwnProperty(m)||ft(t,e,m,null,l,f)}}for(var p in l){var m=l[p];if(f=n[p],l.hasOwnProperty(p)&&(m!=null||f!=null))switch(p){case"type":r=m;break;case"name":i=m;break;case"checked":c=m;break;case"defaultChecked":s=m;break;case"value":a=m;break;case"defaultValue":o=m;break;case"children":case"dangerouslySetInnerHTML":if(m!=null)throw Error(M(137,e));break;default:m!==f&&ft(t,e,p,m,l,f)}}Vc(t,a,o,u,c,s,r,i);return;case"select":m=a=o=p=null;for(r in n)if(u=n[r],n.hasOwnProperty(r)&&u!=null)switch(r){case"value":break;case"multiple":m=u;default:l.hasOwnProperty(r)||ft(t,e,r,null,l,u)}for(i in l)if(r=l[i],u=n[i],l.hasOwnProperty(i)&&(r!=null||u!=null))switch(i){case"value":p=r;break;case"defaultValue":o=r;break;case"multiple":a=r;default:r!==u&&ft(t,e,i,r,l,u)}e=o,n=a,l=m,p!=null?yi(t,!!n,p,!1):!!l!=!!n&&(e!=null?yi(t,!!n,e,!0):yi(t,!!n,n?[]:"",!1));return;case"textarea":m=p=null;for(o in n)if(i=n[o],n.hasOwnProperty(o)&&i!=null&&!l.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:ft(t,e,o,null,l,i)}for(a in l)if(i=l[a],r=n[a],l.hasOwnProperty(a)&&(i!=null||r!=null))switch(a){case"value":p=i;break;case"defaultValue":m=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(M(91));break;default:i!==r&&ft(t,e,a,i,l,r)}Nd(t,p,m);return;case"option":for(var y in n)if(p=n[y],n.hasOwnProperty(y)&&p!=null&&!l.hasOwnProperty(y))switch(y){case"selected":t.selected=!1;break;default:ft(t,e,y,null,l,p)}for(u in l)if(p=l[u],m=n[u],l.hasOwnProperty(u)&&p!==m&&(p!=null||m!=null))switch(u){case"selected":t.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:ft(t,e,u,p,l,m)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)p=n[v],n.hasOwnProperty(v)&&p!=null&&!l.hasOwnProperty(v)&&ft(t,e,v,null,l,p);for(c in l)if(p=l[c],m=n[c],l.hasOwnProperty(c)&&p!==m&&(p!=null||m!=null))switch(c){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(M(137,e));break;default:ft(t,e,c,p,l,m)}return;default:if(Ls(e)){for(var E in n)p=n[E],n.hasOwnProperty(E)&&p!==void 0&&!l.hasOwnProperty(E)&&xs(t,e,E,void 0,l,p);for(s in l)p=l[s],m=n[s],!l.hasOwnProperty(s)||p===m||p===void 0&&m===void 0||xs(t,e,s,p,l,m);return}}for(var h in n)p=n[h],n.hasOwnProperty(h)&&p!=null&&!l.hasOwnProperty(h)&&ft(t,e,h,null,l,p);for(f in l)p=l[f],m=n[f],!l.hasOwnProperty(f)||p===m||p==null&&m==null||ft(t,e,f,p,l,m)}var vs=null,Ss=null;function Lo(t){return t.nodeType===9?t:t.ownerDocument}function ed(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function By(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ks(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var _c=null;function GS(){var t=window.event;return t&&t.type==="popstate"?t===_c?!1:(_c=t,!0):(_c=null,!1)}var Hy=typeof setTimeout=="function"?setTimeout:void 0,YS=typeof clearTimeout=="function"?clearTimeout:void 0,nd=typeof Promise=="function"?Promise:void 0,VS=typeof queueMicrotask=="function"?queueMicrotask:typeof nd!="undefined"?function(t){return nd.resolve(null).then(t).catch(XS)}:Hy;function XS(t){setTimeout(function(){throw t})}function ll(t){return t==="head"}function ld(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var a=t.ownerDocument;if(n&1&&_r(a.documentElement),n&2&&_r(a.body),n&4)for(n=a.head,_r(n),a=n.firstChild;a;){var o=a.nextSibling,u=a.nodeName;a[Pr]||u==="SCRIPT"||u==="STYLE"||u==="LINK"&&a.rel.toLowerCase()==="stylesheet"||n.removeChild(a),a=o}}if(i===0){t.removeChild(r),Ir(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Ir(e)}function ws(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ws(n),Ns(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function QS(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Pr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ve(t.nextSibling),t===null)break}return null}function ZS(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ve(t.nextSibling),t===null))return null;return t}function Es(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function IS(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Ve(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Ts=null;function id(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function qy(t,e,n){switch(e=Lo(n),t){case"html":if(t=e.documentElement,!t)throw Error(M(452));return t;case"head":if(t=e.head,!t)throw Error(M(453));return t;case"body":if(t=e.body,!t)throw Error(M(454));return t;default:throw Error(M(451))}}function _r(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ns(t)}var Ue=new Map,rd=new Set;function Uo(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Tn=ot.d;ot.d={f:FS,r:KS,D:JS,C:WS,L:PS,m:$S,X:ek,S:tk,M:nk};function FS(){var t=Tn.f(),e=Wo();return t||e}function KS(t){var e=Ui(t);e!==null&&e.tag===5&&e.type==="form"?Rg(e):Tn.r(t)}var qi=typeof document=="undefined"?null:document;function jy(t,e,n){var l=qi;if(l&&typeof e=="string"&&e){var i=Re(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),rd.has(i)||(rd.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),Pt(e,"link",t),Xt(e),l.head.appendChild(e)))}}function JS(t){Tn.D(t),jy("dns-prefetch",t,null)}function WS(t,e){Tn.C(t,e),jy("preconnect",t,e)}function PS(t,e,n){Tn.L(t,e,n);var l=qi;if(l&&t&&e){var i='link[rel="preload"][as="'+Re(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Re(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Re(n.imageSizes)+'"]')):i+='[href="'+Re(t)+'"]';var r=i;switch(e){case"style":r=Ni(t);break;case"script":r=ji(t)}Ue.has(r)||(t=yt({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ue.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(ua(r))||e==="script"&&l.querySelector(ca(r))||(e=l.createElement("link"),Pt(e,"link",t),Xt(e),l.head.appendChild(e)))}}function $S(t,e){Tn.m(t,e);var n=qi;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+Re(l)+'"][href="'+Re(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=ji(t)}if(!Ue.has(r)&&(t=yt({rel:"modulepreload",href:t},e),Ue.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ca(r)))return}l=n.createElement("link"),Pt(l,"link",t),Xt(l),n.head.appendChild(l)}}}function tk(t,e,n){Tn.S(t,e,n);var l=qi;if(l&&t){var i=gi(l).hoistableStyles,r=Ni(t);e=e||"default";var a=i.get(r);if(!a){var o={loading:0,preload:null};if(a=l.querySelector(ua(r)))o.loading=5;else{t=yt({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ue.get(r))&&bf(t,n);var u=a=l.createElement("link");Xt(u),Pt(u,"link",t),u._p=new Promise(function(c,s){u.onload=c,u.onerror=s}),u.addEventListener("load",function(){o.loading|=1}),u.addEventListener("error",function(){o.loading|=2}),o.loading|=4,oo(a,e,l)}a={type:"stylesheet",instance:a,count:1,state:o},i.set(r,a)}}}function ek(t,e){Tn.X(t,e);var n=qi;if(n&&t){var l=gi(n).hoistableScripts,i=ji(t),r=l.get(i);r||(r=n.querySelector(ca(i)),r||(t=yt({src:t,async:!0},e),(e=Ue.get(i))&&xf(t,e),r=n.createElement("script"),Xt(r),Pt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function nk(t,e){Tn.M(t,e);var n=qi;if(n&&t){var l=gi(n).hoistableScripts,i=ji(t),r=l.get(i);r||(r=n.querySelector(ca(i)),r||(t=yt({src:t,async:!0,type:"module"},e),(e=Ue.get(i))&&xf(t,e),r=n.createElement("script"),Xt(r),Pt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function ad(t,e,n,l){var i=(i=Qn.current)?Uo(i):null;if(!i)throw Error(M(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ni(n.href),n=gi(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ni(n.href);var r=gi(i).hoistableStyles,a=r.get(t);if(a||(i=i.ownerDocument||i,a={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,a),(r=i.querySelector(ua(t)))&&!r._p&&(a.instance=r,a.state.loading=5),Ue.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ue.set(t,n),r||lk(i,t,n,a.state))),e&&l===null)throw Error(M(528,""));return a}if(e&&l!==null)throw Error(M(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=ji(n),n=gi(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(M(444,t))}}function Ni(t){return'href="'+Re(t)+'"'}function ua(t){return'link[rel="stylesheet"]['+t+"]"}function Gy(t){return yt({},t,{"data-precedence":t.precedence,precedence:null})}function lk(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Pt(e,"link",n),Xt(e),t.head.appendChild(e))}function ji(t){return'[src="'+Re(t)+'"]'}function ca(t){return"script[async]"+t}function od(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Re(n.href)+'"]');if(l)return e.instance=l,Xt(l),l;var i=yt({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Xt(l),Pt(l,"style",i),oo(l,n.precedence,t),e.instance=l;case"stylesheet":i=Ni(n.href);var r=t.querySelector(ua(i));if(r)return e.state.loading|=4,e.instance=r,Xt(r),r;l=Gy(n),(i=Ue.get(i))&&bf(l,i),r=(t.ownerDocument||t).createElement("link"),Xt(r);var a=r;return a._p=new Promise(function(o,u){a.onload=o,a.onerror=u}),Pt(r,"link",l),e.state.loading|=4,oo(r,n.precedence,t),e.instance=r;case"script":return r=ji(n.src),(i=t.querySelector(ca(r)))?(e.instance=i,Xt(i),i):(l=n,(i=Ue.get(r))&&(l=yt({},n),xf(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),Xt(i),Pt(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(M(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(l=e.instance,e.state.loading|=4,oo(l,n.precedence,t));return e.instance}function oo(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,a=0;a<l.length;a++){var o=l[a];if(o.dataset.precedence===e)r=o;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function bf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function xf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var uo=null;function ud(t,e,n){if(uo===null){var l=new Map,i=uo=new Map;i.set(n,l)}else i=uo,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[Pr]||r[te]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var a=r.getAttribute(e)||"";a=t+a;var o=l.get(a);o?o.push(r):l.set(a,[r])}}return l}function cd(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function ik(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Yy(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var Vr=null;function rk(){}function ak(t,e,n){if(Vr===null)throw Error(M(475));var l=Vr;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var i=Ni(n.href),r=t.querySelector(ua(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Bo.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,Xt(r);return}r=t.ownerDocument||t,n=Gy(n),(i=Ue.get(i))&&bf(n,i),r=r.createElement("link"),Xt(r);var a=r;a._p=new Promise(function(o,u){a.onload=o,a.onerror=u}),Pt(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(l.count++,e=Bo.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function ok(){if(Vr===null)throw Error(M(475));var t=Vr;return t.stylesheets&&t.count===0&&As(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&As(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Bo(){if(this.count--,this.count===0){if(this.stylesheets)As(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ho=null;function As(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ho=new Map,e.forEach(uk,t),Ho=null,Bo.call(t))}function uk(t,e){if(!(e.state.loading&4)){var n=Ho.get(t);if(n)var l=n.get(null);else{n=new Map,Ho.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var a=i[r];(a.nodeName==="LINK"||a.getAttribute("media")!=="not all")&&(n.set(a.dataset.precedence,a),l=a)}l&&n.set(null,l)}i=e.instance,a=i.getAttribute("data-precedence"),r=n.get(a)||l,r===l&&n.set(null,i),n.set(a,i),this.count++,l=Bo.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Xr={$$typeof:hn,Provider:null,Consumer:null,_currentValue:yl,_currentValue2:yl,_threadCount:0};function ck(t,e,n,l,i,r,a,o){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ic(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ic(0),this.hiddenUpdates=ic(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Vy(t,e,n,l,i,r,a,o,u,c,s,f){return t=new ck(t,e,n,a,o,u,c,f),e=1,r===!0&&(e|=24),r=Se(3,null,null,e),t.current=r,r.stateNode=t,e=Zs(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Fs(r),t}function Xy(t){return t?(t=mi,t):mi}function Qy(t,e,n,l,i,r){i=Xy(i),l.context===null?l.context=i:l.pendingContext=i,l=Zn(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=In(t,l,e),n!==null&&(Te(n,t,e),Tr(n,t,e))}function sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function vf(t,e){sd(t,e),(t=t.alternate)&&sd(t,e)}function Zy(t){if(t.tag===13){var e=Bi(t,67108864);e!==null&&Te(e,t,67108864),vf(t,67108864)}}var qo=!0;function sk(t,e,n,l){var i=Q.T;Q.T=null;var r=ot.p;try{ot.p=2,Sf(t,e,n,l)}finally{ot.p=r,Q.T=i}}function fk(t,e,n,l){var i=Q.T;Q.T=null;var r=ot.p;try{ot.p=8,Sf(t,e,n,l)}finally{ot.p=r,Q.T=i}}function Sf(t,e,n,l){if(qo){var i=zs(l);if(i===null)Rc(t,e,l,jo,n),fd(t,l);else if(pk(i,t,e,n,l))l.stopPropagation();else if(fd(t,l),e&4&&-1<mk.indexOf(t)){for(;i!==null;){var r=Ui(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var a=hl(r.pendingLanes);if(a!==0){var o=r;for(o.pendingLanes|=2,o.entangledLanes|=2;a;){var u=1<<31-we(a);o.entanglements[1]|=u,a&=~u}nn(r),!(ct&6)&&(Do=$e()+500,oa(0,!1))}}break;case 13:o=Bi(r,2),o!==null&&Te(o,r,2),Wo(),vf(r,2)}if(r=zs(l),r===null&&Rc(t,e,l,jo,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else Rc(t,e,l,null,n)}}function zs(t){return t=Us(t),kf(t)}var jo=null;function kf(t){if(jo=null,t=ai(t),t!==null){var e=Fr(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=yd(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return jo=t,null}function Iy(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch($x()){case Sd:return 2;case kd:return 8;case po:case tv:return 32;case wd:return 268435456;default:return 32}default:return 32}}var Cs=!1,Jn=null,Wn=null,Pn=null,Qr=new Map,Zr=new Map,jn=[],mk="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function fd(t,e){switch(t){case"focusin":case"focusout":Jn=null;break;case"dragenter":case"dragleave":Wn=null;break;case"mouseover":case"mouseout":Pn=null;break;case"pointerover":case"pointerout":Qr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zr.delete(e.pointerId)}}function hr(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=Ui(e),e!==null&&Zy(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function pk(t,e,n,l,i){switch(e){case"focusin":return Jn=hr(Jn,t,e,n,l,i),!0;case"dragenter":return Wn=hr(Wn,t,e,n,l,i),!0;case"mouseover":return Pn=hr(Pn,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return Qr.set(r,hr(Qr.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Zr.set(r,hr(Zr.get(r)||null,t,e,n,l,i)),!0}return!1}function Fy(t){var e=ai(t.target);if(e!==null){var n=Fr(e);if(n!==null){if(e=n.tag,e===13){if(e=yd(n),e!==null){t.blockedOn=e,uv(t.priority,function(){if(n.tag===13){var l=Ee();l=Rs(l);var i=Bi(n,l);i!==null&&Te(i,n,l),vf(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function co(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=zs(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Qc=l,n.target.dispatchEvent(l),Qc=null}else return e=Ui(n),e!==null&&Zy(e),t.blockedOn=n,!1;e.shift()}return!0}function md(t,e,n){co(t)&&n.delete(e)}function hk(){Cs=!1,Jn!==null&&co(Jn)&&(Jn=null),Wn!==null&&co(Wn)&&(Wn=null),Pn!==null&&co(Pn)&&(Pn=null),Qr.forEach(md),Zr.forEach(md)}function Ka(t,e){t.blockedOn===e&&(t.blockedOn=null,Cs||(Cs=!0,jt.unstable_scheduleCallback(jt.unstable_NormalPriority,hk)))}var Ja=null;function pd(t){Ja!==t&&(Ja=t,jt.unstable_scheduleCallback(jt.unstable_NormalPriority,function(){Ja===t&&(Ja=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(kf(l||n)===null)continue;break}var r=Ui(n);r!==null&&(t.splice(e,3),e-=3,os(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Ir(t){function e(u){return Ka(u,t)}Jn!==null&&Ka(Jn,t),Wn!==null&&Ka(Wn,t),Pn!==null&&Ka(Pn,t),Qr.forEach(e),Zr.forEach(e);for(var n=0;n<jn.length;n++){var l=jn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<jn.length&&(n=jn[0],n.blockedOn===null);)Fy(n),n.blockedOn===null&&jn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],a=i[he]||null;if(typeof r=="function")a||pd(n);else if(a){var o=null;if(r&&r.hasAttribute("formAction")){if(i=r,a=r[he]||null)o=a.formAction;else if(kf(i)!==null)continue}else o=a.action;typeof o=="function"?n[l+1]=o:(n.splice(l,3),l-=3),pd(n)}}}function wf(t){this._internalRoot=t}eu.prototype.render=wf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(M(409));var n=e.current,l=Ee();Qy(n,l,t,e,null,null)};eu.prototype.unmount=wf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Qy(t.current,2,null,t,null,null),Wo(),e[Li]=null}};function eu(t){this._internalRoot=t}eu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Cd();t={blockedOn:null,target:t,priority:e};for(var n=0;n<jn.length&&e!==0&&e<jn[n].priority;n++);jn.splice(n,0,t),n===0&&Fy(t)}};var hd=dd.version;if(hd!=="19.1.0")throw Error(M(527,hd,"19.1.0"));ot.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(M(188)):(t=Object.keys(t).join(","),Error(M(268,t)));return t=Zx(e),t=t!==null?bd(t):null,t=t===null?null:t.stateNode,t};var dk={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:Q,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(dr=__REACT_DEVTOOLS_GLOBAL_HOOK__,!dr.isDisabled&&dr.supportsFiber))try{Kr=dr.inject(dk),ke=dr}catch(t){}var dr;nu.createRoot=function(t,e){if(!gd(t))throw Error(M(299));var n=!1,l="",i=Vg,r=Xg,a=Qg,o=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(a=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(o=e.unstable_transitionCallbacks)),e=Vy(t,1,!1,null,null,n,l,i,r,a,o,null),t[Li]=e.current,yf(t),new wf(e)};nu.hydrateRoot=function(t,e,n){if(!gd(t))throw Error(M(299));var l=!1,i="",r=Vg,a=Xg,o=Qg,u=null,c=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(a=n.onCaughtError),n.onRecoverableError!==void 0&&(o=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(u=n.unstable_transitionCallbacks),n.formState!==void 0&&(c=n.formState)),e=Vy(t,1,!0,e,n!=null?n:null,l,i,r,a,o,u,c),e.context=Xy(null),n=e.current,l=Ee(),l=Rs(l),i=Zn(l),i.callback=null,In(n,i,l),n=l,e.current.lanes=n,Wr(e,n),nn(e),t[Li]=e.current,yf(t),new eu(e)};nu.version="19.1.0"});var Py=ie((BA,Wy)=>{"use strict";function Jy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Jy)}catch(t){console.error(t)}}Jy(),Wy.exports=Ky()});var mb=ie((Yz,fb)=>{var ob=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,Tk=/\n/g,Ak=/^\s*/,zk=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,Ck=/^:\s*/,Mk=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,Dk=/^[;\s]*/,Ok=/^\s+|\s+$/g,Rk=`
`,ub="/",cb="*",Nl="",_k="comment",Nk="declaration";fb.exports=function(t,e){if(typeof t!="string")throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var n=1,l=1;function i(v){var E=v.match(Tk);E&&(n+=E.length);var h=v.lastIndexOf(Rk);l=~h?v.length-h:l+v.length}function r(){var v={line:n,column:l};return function(E){return E.position=new a(v),s(),E}}function a(v){this.start=v,this.end={line:n,column:l},this.source=e.source}a.prototype.content=t;var o=[];function u(v){var E=new Error(e.source+":"+n+":"+l+": "+v);if(E.reason=v,E.filename=e.source,E.line=n,E.column=l,E.source=t,e.silent)o.push(E);else throw E}function c(v){var E=v.exec(t);if(E){var h=E[0];return i(h),t=t.slice(h.length),E}}function s(){c(Ak)}function f(v){var E;for(v=v||[];E=p();)E!==!1&&v.push(E);return v}function p(){var v=r();if(!(ub!=t.charAt(0)||cb!=t.charAt(1))){for(var E=2;Nl!=t.charAt(E)&&(cb!=t.charAt(E)||ub!=t.charAt(E+1));)++E;if(E+=2,Nl===t.charAt(E-1))return u("End of comment missing");var h=t.slice(2,E-2);return l+=2,i(h),t=t.slice(E),l+=2,v({type:_k,comment:h})}}function m(){var v=r(),E=c(zk);if(E){if(p(),!c(Ck))return u("property missing ':'");var h=c(Mk),d=v({type:Nk,property:sb(E[0].replace(ob,Nl)),value:h?sb(h[0].replace(ob,Nl)):Nl});return c(Dk),d}}function y(){var v=[];f(v);for(var E;E=m();)E!==!1&&(v.push(E),f(v));return v}return s(),y()};function sb(t){return t?t.replace(Ok,Nl):Nl}});var pb=ie(ma=>{"use strict";var Lk=ma&&ma.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ma,"__esModule",{value:!0});ma.default=Bk;var Uk=Lk(mb());function Bk(t,e){var n=null;if(!t||typeof t!="string")return n;var l=(0,Uk.default)(t),i=typeof e=="function";return l.forEach(function(r){if(r.type==="declaration"){var a=r.property,o=r.value;i?e(a,o,r):o&&(n=n||{},n[a]=o)}}),n}});var db=ie(ou=>{"use strict";Object.defineProperty(ou,"__esModule",{value:!0});ou.camelCase=void 0;var Hk=/^--[a-zA-Z0-9_-]+$/,qk=/-([a-z])/g,jk=/^[^-]+$/,Gk=/^-(webkit|moz|ms|o|khtml)-/,Yk=/^-(ms)-/,Vk=function(t){return!t||jk.test(t)||Hk.test(t)},Xk=function(t,e){return e.toUpperCase()},hb=function(t,e){return"".concat(e,"-")},Qk=function(t,e){return e===void 0&&(e={}),Vk(t)?t:(t=t.toLowerCase(),e.reactCompat?t=t.replace(Yk,hb):t=t.replace(Gk,hb),t.replace(qk,Xk))};ou.camelCase=Qk});var yb=ie((Lf,gb)=>{"use strict";var Zk=Lf&&Lf.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Ik=Zk(pb()),Fk=db();function Nf(t,e){var n={};return!t||typeof t!="string"||(0,Ik.default)(t,function(l,i){l&&i&&(n[(0,Fk.camelCase)(l,e)]=i)}),n}Nf.default=Nf;gb.exports=Nf});var zb=ie(cu=>{"use strict";var hw=Symbol.for("react.transitional.element"),dw=Symbol.for("react.fragment");function Ab(t,e,n){var l=null;if(n!==void 0&&(l=""+n),e.key!==void 0&&(l=""+e.key),"key"in e){n={};for(var i in e)i!=="key"&&(n[i]=e[i])}else n=e;return e=n.ref,{$$typeof:hw,type:t,key:l,ref:e!==void 0?e:null,props:n}}cu.Fragment=dw;cu.jsx=Ab;cu.jsxs=Ab});var Yi=ie((gC,Cb)=>{"use strict";Cb.exports=zb()});var X1=ie((OO,V1)=>{"use strict";var Ru=Object.prototype.hasOwnProperty,Y1=Object.prototype.toString,U1=Object.defineProperty,B1=Object.getOwnPropertyDescriptor,H1=function(e){return typeof Array.isArray=="function"?Array.isArray(e):Y1.call(e)==="[object Array]"},q1=function(e){if(!e||Y1.call(e)!=="[object Object]")return!1;var n=Ru.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&Ru.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!l)return!1;var i;for(i in e);return typeof i=="undefined"||Ru.call(e,i)},j1=function(e,n){U1&&n.name==="__proto__"?U1(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},G1=function(e,n){if(n==="__proto__")if(Ru.call(e,n)){if(B1)return B1(e,n).value}else return;return e[n]};V1.exports=function t(){var e,n,l,i,r,a,o=arguments[0],u=1,c=arguments.length,s=!1;for(typeof o=="boolean"&&(s=o,o=arguments[1]||{},u=2),(o==null||typeof o!="object"&&typeof o!="function")&&(o={});u<c;++u)if(e=arguments[u],e!=null)for(n in e)l=G1(o,n),i=G1(e,n),o!==i&&(s&&i&&(q1(i)||(r=H1(i)))?(r?(r=!1,a=l&&H1(l)?l:[]):a=l&&q1(l)?l:{},j1(o,{name:n,newValue:t(s,a,i)})):typeof i!="undefined"&&j1(o,{name:n,newValue:i}));return o}});var Ft=Ie(lr()),sx=Ie(Py());function $y(t,e){let n=e||{};return(t[t.length-1]===""?[...t,""]:t).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}var gk=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,yk=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,bk={};function lu(t,e){return((e||bk).jsx?yk:gk).test(t)}var xk=/[ \t\n\f\r]/g;function Ef(t){return typeof t=="object"?t.type==="text"?tb(t.value):!1:tb(t)}function tb(t){return t.replace(xk,"")===""}var An=class{constructor(e,n,l){this.normal=n,this.property=e,l&&(this.space=l)}};An.prototype.normal={};An.prototype.property={};An.prototype.space=void 0;function Tf(t,e){let n={},l={};for(let i of t)Object.assign(n,i.property),Object.assign(l,i.normal);return new An(n,l,e)}function sa(t){return t.toLowerCase()}var It=class{constructor(e,n){this.attribute=n,this.property=e}};It.prototype.attribute="";It.prototype.booleanish=!1;It.prototype.boolean=!1;It.prototype.commaOrSpaceSeparated=!1;It.prototype.commaSeparated=!1;It.prototype.defined=!1;It.prototype.mustUseProperty=!1;It.prototype.number=!1;It.prototype.overloadedBoolean=!1;It.prototype.property="";It.prototype.spaceSeparated=!1;It.prototype.space=void 0;var fa={};yp(fa,{boolean:()=>Z,booleanish:()=>Ct,commaOrSpaceSeparated:()=>ge,commaSeparated:()=>il,number:()=>O,overloadedBoolean:()=>Af,spaceSeparated:()=>st});var vk=0,Z=Rl(),Ct=Rl(),Af=Rl(),O=Rl(),st=Rl(),il=Rl(),ge=Rl();function Rl(){return gp(2,++vk)}var zf=Object.keys(fa),_l=class extends It{constructor(e,n,l,i){let r=-1;if(super(e,n),eb(this,"space",i),typeof l=="number")for(;++r<zf.length;){let a=zf[r];eb(this,zf[r],(l&fa[a])===fa[a])}}};_l.prototype.defined=!0;function eb(t,e,n){n&&(t[e]=n)}function Be(t){let e={},n={};for(let[l,i]of Object.entries(t.properties)){let r=new _l(l,t.transform(t.attributes||{},l),i,t.space);t.mustUseProperty&&t.mustUseProperty.includes(l)&&(r.mustUseProperty=!0),e[l]=r,n[sa(l)]=l,n[sa(r.attribute)]=l}return new An(e,n,t.space)}var Cf=Be({properties:{ariaActiveDescendant:null,ariaAtomic:Ct,ariaAutoComplete:null,ariaBusy:Ct,ariaChecked:Ct,ariaColCount:O,ariaColIndex:O,ariaColSpan:O,ariaControls:st,ariaCurrent:null,ariaDescribedBy:st,ariaDetails:null,ariaDisabled:Ct,ariaDropEffect:st,ariaErrorMessage:null,ariaExpanded:Ct,ariaFlowTo:st,ariaGrabbed:Ct,ariaHasPopup:null,ariaHidden:Ct,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:st,ariaLevel:O,ariaLive:null,ariaModal:Ct,ariaMultiLine:Ct,ariaMultiSelectable:Ct,ariaOrientation:null,ariaOwns:st,ariaPlaceholder:null,ariaPosInSet:O,ariaPressed:Ct,ariaReadOnly:Ct,ariaRelevant:null,ariaRequired:Ct,ariaRoleDescription:st,ariaRowCount:O,ariaRowIndex:O,ariaRowSpan:O,ariaSelected:Ct,ariaSetSize:O,ariaSort:null,ariaValueMax:O,ariaValueMin:O,ariaValueNow:O,ariaValueText:null,role:null},transform(t,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function iu(t,e){return e in t?t[e]:e}function ru(t,e){return iu(t,e.toLowerCase())}var nb=Be({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:il,acceptCharset:st,accessKey:st,action:null,allow:null,allowFullScreen:Z,allowPaymentRequest:Z,allowUserMedia:Z,alt:null,as:null,async:Z,autoCapitalize:null,autoComplete:st,autoFocus:Z,autoPlay:Z,blocking:st,capture:null,charSet:null,checked:Z,cite:null,className:st,cols:O,colSpan:null,content:null,contentEditable:Ct,controls:Z,controlsList:st,coords:O|il,crossOrigin:null,data:null,dateTime:null,decoding:null,default:Z,defer:Z,dir:null,dirName:null,disabled:Z,download:Af,draggable:Ct,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:Z,formTarget:null,headers:st,height:O,hidden:Z,high:O,href:null,hrefLang:null,htmlFor:st,httpEquiv:st,id:null,imageSizes:null,imageSrcSet:null,inert:Z,inputMode:null,integrity:null,is:null,isMap:Z,itemId:null,itemProp:st,itemRef:st,itemScope:Z,itemType:st,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:Z,low:O,manifest:null,max:null,maxLength:O,media:null,method:null,min:null,minLength:O,multiple:Z,muted:Z,name:null,nonce:null,noModule:Z,noValidate:Z,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:Z,optimum:O,pattern:null,ping:st,placeholder:null,playsInline:Z,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:Z,referrerPolicy:null,rel:st,required:Z,reversed:Z,rows:O,rowSpan:O,sandbox:st,scope:null,scoped:Z,seamless:Z,selected:Z,shadowRootClonable:Z,shadowRootDelegatesFocus:Z,shadowRootMode:null,shape:null,size:O,sizes:null,slot:null,span:O,spellCheck:Ct,src:null,srcDoc:null,srcLang:null,srcSet:null,start:O,step:null,style:null,tabIndex:O,target:null,title:null,translate:null,type:null,typeMustMatch:Z,useMap:null,value:Ct,width:O,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:st,axis:null,background:null,bgColor:null,border:O,borderColor:null,bottomMargin:O,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:Z,declare:Z,event:null,face:null,frame:null,frameBorder:null,hSpace:O,leftMargin:O,link:null,longDesc:null,lowSrc:null,marginHeight:O,marginWidth:O,noResize:Z,noHref:Z,noShade:Z,noWrap:Z,object:null,profile:null,prompt:null,rev:null,rightMargin:O,rules:null,scheme:null,scrolling:Ct,standby:null,summary:null,text:null,topMargin:O,valueType:null,version:null,vAlign:null,vLink:null,vSpace:O,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:Z,disableRemotePlayback:Z,prefix:null,property:null,results:O,security:null,unselectable:null},space:"html",transform:ru});var lb=Be({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:ge,accentHeight:O,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:O,amplitude:O,arabicForm:null,ascent:O,attributeName:null,attributeType:null,azimuth:O,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:O,by:null,calcMode:null,capHeight:O,className:st,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:O,diffuseConstant:O,direction:null,display:null,dur:null,divisor:O,dominantBaseline:null,download:Z,dx:null,dy:null,edgeMode:null,editable:null,elevation:O,enableBackground:null,end:null,event:null,exponent:O,externalResourcesRequired:null,fill:null,fillOpacity:O,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:il,g2:il,glyphName:il,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:O,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:O,horizOriginX:O,horizOriginY:O,id:null,ideographic:O,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:O,k:O,k1:O,k2:O,k3:O,k4:O,kernelMatrix:ge,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:O,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:O,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:O,overlineThickness:O,paintOrder:null,panose1:null,path:null,pathLength:O,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:st,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:O,pointsAtY:O,pointsAtZ:O,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:ge,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:ge,rev:ge,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:ge,requiredFeatures:ge,requiredFonts:ge,requiredFormats:ge,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:O,specularExponent:O,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:O,strikethroughThickness:O,string:null,stroke:null,strokeDashArray:ge,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:O,strokeOpacity:O,strokeWidth:null,style:null,surfaceScale:O,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:ge,tabIndex:O,tableValues:null,target:null,targetX:O,targetY:O,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:ge,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:O,underlineThickness:O,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:O,values:null,vAlphabetic:O,vMathematical:O,vectorEffect:null,vHanging:O,vIdeographic:O,version:null,vertAdvY:O,vertOriginX:O,vertOriginY:O,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:O,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:iu});var Mf=Be({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(t,e){return"xlink:"+e.slice(5).toLowerCase()}});var Df=Be({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:ru});var Of=Be({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(t,e){return"xml:"+e.slice(3).toLowerCase()}});var Rf={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var Sk=/[A-Z]/g,ib=/-[a-z]/g,kk=/^data[-\w.:]+$/i;function _f(t,e){let n=sa(e),l=e,i=It;if(n in t.normal)return t.property[t.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&kk.test(e)){if(e.charAt(4)==="-"){let r=e.slice(5).replace(ib,Ek);l="data"+r.charAt(0).toUpperCase()+r.slice(1)}else{let r=e.slice(4);if(!ib.test(r)){let a=r.replace(Sk,wk);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=_l}return new i(l,e)}function wk(t){return"-"+t.toLowerCase()}function Ek(t){return t.charAt(1).toUpperCase()}var rb=Tf([Cf,nb,Mf,Df,Of],"html"),au=Tf([Cf,lb,Mf,Df,Of],"svg");function ab(t){return t.join(" ").trim()}var Sb=Ie(yb(),1);var uu=bb("end"),Gi=bb("start");function bb(t){return e;function e(n){let l=n&&n.position&&n.position[t]||{};if(typeof l.line=="number"&&l.line>0&&typeof l.column=="number"&&l.column>0)return{line:l.line,column:l.column,offset:typeof l.offset=="number"&&l.offset>-1?l.offset:void 0}}}function Uf(t){let e=Gi(t),n=uu(t);if(e&&n)return{start:e,end:n}}function rl(t){return!t||typeof t!="object"?"":"position"in t||"type"in t?xb(t.position):"start"in t||"end"in t?xb(t):"line"in t||"column"in t?Bf(t):""}function Bf(t){return vb(t&&t.line)+":"+vb(t&&t.column)}function xb(t){return Bf(t&&t.start)+"-"+Bf(t&&t.end)}function vb(t){return t&&typeof t=="number"?t:1}var _t=class extends Error{constructor(e,n,l){super(),typeof n=="string"&&(l=n,n=void 0);let i="",r={},a=!1;if(n&&("line"in n&&"column"in n?r={place:n}:"start"in n&&"end"in n?r={place:n}:"type"in n?r={ancestors:[n],place:n.position}:r=C({},n)),typeof e=="string"?i=e:!r.cause&&e&&(a=!0,i=e.message,r.cause=e),!r.ruleId&&!r.source&&typeof l=="string"){let u=l.indexOf(":");u===-1?r.ruleId=l:(r.source=l.slice(0,u),r.ruleId=l.slice(u+1))}if(!r.place&&r.ancestors&&r.ancestors){let u=r.ancestors[r.ancestors.length-1];u&&(r.place=u.position)}let o=r.place&&"start"in r.place?r.place.start:r.place;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=o?o.line:void 0,this.name=rl(r.place)||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=a&&r.cause&&typeof r.cause.stack=="string"?r.cause.stack:"",this.actual,this.expected,this.note,this.url}};_t.prototype.file="";_t.prototype.name="";_t.prototype.reason="";_t.prototype.message="";_t.prototype.stack="";_t.prototype.column=void 0;_t.prototype.line=void 0;_t.prototype.ancestors=void 0;_t.prototype.cause=void 0;_t.prototype.fatal=void 0;_t.prototype.place=void 0;_t.prototype.ruleId=void 0;_t.prototype.source=void 0;var Hf={}.hasOwnProperty,Kk=new Map,Jk=/[A-Z]/g,Wk=new Set(["table","tbody","thead","tfoot","tr"]),Pk=new Set(["td","th"]),kb="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function qf(t,e){if(!e||e.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");let n=e.filePath||void 0,l;if(e.development){if(typeof e.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");l=aw(n,e.jsxDEV)}else{if(typeof e.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof e.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");l=rw(n,e.jsx,e.jsxs)}let i={Fragment:e.Fragment,ancestors:[],components:e.components||{},create:l,elementAttributeNameCase:e.elementAttributeNameCase||"react",evaluater:e.createEvaluater?e.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:e.ignoreInvalidStyle||!1,passKeys:e.passKeys!==!1,passNode:e.passNode||!1,schema:e.space==="svg"?au:rb,stylePropertyNameCase:e.stylePropertyNameCase||"dom",tableCellAlignToStyle:e.tableCellAlignToStyle!==!1},r=wb(i,t,void 0);return r&&typeof r!="string"?r:i.create(t,i.Fragment,{children:r||void 0},void 0)}function wb(t,e,n){if(e.type==="element")return $k(t,e,n);if(e.type==="mdxFlowExpression"||e.type==="mdxTextExpression")return tw(t,e);if(e.type==="mdxJsxFlowElement"||e.type==="mdxJsxTextElement")return nw(t,e,n);if(e.type==="mdxjsEsm")return ew(t,e);if(e.type==="root")return lw(t,e,n);if(e.type==="text")return iw(t,e)}function $k(t,e,n){let l=t.schema,i=l;e.tagName.toLowerCase()==="svg"&&l.space==="html"&&(i=au,t.schema=i),t.ancestors.push(e);let r=Tb(t,e.tagName,!1),a=ow(t,e),o=Gf(t,e);return Wk.has(e.tagName)&&(o=o.filter(function(u){return typeof u=="string"?!Ef(u):!0})),Eb(t,a,r,e),jf(a,o),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function tw(t,e){if(e.data&&e.data.estree&&t.evaluater){let l=e.data.estree.body[0];return l.type,t.evaluater.evaluateExpression(l.expression)}pa(t,e.position)}function ew(t,e){if(e.data&&e.data.estree&&t.evaluater)return t.evaluater.evaluateProgram(e.data.estree);pa(t,e.position)}function nw(t,e,n){let l=t.schema,i=l;e.name==="svg"&&l.space==="html"&&(i=au,t.schema=i),t.ancestors.push(e);let r=e.name===null?t.Fragment:Tb(t,e.name,!0),a=uw(t,e),o=Gf(t,e);return Eb(t,a,r,e),jf(a,o),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function lw(t,e,n){let l={};return jf(l,Gf(t,e)),t.create(e,t.Fragment,l,n)}function iw(t,e){return e.value}function Eb(t,e,n,l){typeof n!="string"&&n!==t.Fragment&&t.passNode&&(e.node=l)}function jf(t,e){if(e.length>0){let n=e.length>1?e:e[0];n&&(t.children=n)}}function rw(t,e,n){return l;function l(i,r,a,o){let c=Array.isArray(a.children)?n:e;return o?c(r,a,o):c(r,a)}}function aw(t,e){return n;function n(l,i,r,a){let o=Array.isArray(r.children),u=Gi(l);return e(i,r,a,o,{columnNumber:u?u.column-1:void 0,fileName:t,lineNumber:u?u.line:void 0},void 0)}}function ow(t,e){let n={},l,i;for(i in e.properties)if(i!=="children"&&Hf.call(e.properties,i)){let r=cw(t,i,e.properties[i]);if(r){let[a,o]=r;t.tableCellAlignToStyle&&a==="align"&&typeof o=="string"&&Pk.has(e.tagName)?l=o:n[a]=o}}if(l){let r=n.style||(n.style={});r[t.stylePropertyNameCase==="css"?"text-align":"textAlign"]=l}return n}function uw(t,e){let n={};for(let l of e.attributes)if(l.type==="mdxJsxExpressionAttribute")if(l.data&&l.data.estree&&t.evaluater){let r=l.data.estree.body[0];r.type;let a=r.expression;a.type;let o=a.properties[0];o.type,Object.assign(n,t.evaluater.evaluateExpression(o.argument))}else pa(t,e.position);else{let i=l.name,r;if(l.value&&typeof l.value=="object")if(l.value.data&&l.value.data.estree&&t.evaluater){let o=l.value.data.estree.body[0];o.type,r=t.evaluater.evaluateExpression(o.expression)}else pa(t,e.position);else r=l.value===null?!0:l.value;n[i]=r}return n}function Gf(t,e){let n=[],l=-1,i=t.passKeys?new Map:Kk;for(;++l<e.children.length;){let r=e.children[l],a;if(t.passKeys){let u=r.type==="element"?r.tagName:r.type==="mdxJsxFlowElement"||r.type==="mdxJsxTextElement"?r.name:void 0;if(u){let c=i.get(u)||0;a=u+"-"+c,i.set(u,c+1)}}let o=wb(t,r,a);o!==void 0&&n.push(o)}return n}function cw(t,e,n){let l=_f(t.schema,e);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=l.commaSeparated?$y(n):ab(n)),l.property==="style"){let i=typeof n=="object"?n:sw(t,String(n));return t.stylePropertyNameCase==="css"&&(i=fw(i)),["style",i]}return[t.elementAttributeNameCase==="react"&&l.space?Rf[l.property]||l.property:l.attribute,n]}}function sw(t,e){try{return(0,Sb.default)(e,{reactCompat:!0})}catch(n){if(t.ignoreInvalidStyle)return{};let l=n,i=new _t("Cannot parse `style` attribute",{ancestors:t.ancestors,cause:l,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=t.filePath||void 0,i.url=kb+"#cannot-parse-style-attribute",i}}function Tb(t,e,n){let l;if(!n)l={type:"Literal",value:e};else if(e.includes(".")){let i=e.split("."),r=-1,a;for(;++r<i.length;){let o=lu(i[r])?{type:"Identifier",name:i[r]}:{type:"Literal",value:i[r]};a=a?{type:"MemberExpression",object:a,property:o,computed:!!(r&&o.type==="Literal"),optional:!1}:o}l=a}else l=lu(e)&&!/^[a-z]/.test(e)?{type:"Identifier",name:e}:{type:"Literal",value:e};if(l.type==="Literal"){let i=l.value;return Hf.call(t.components,i)?t.components[i]:i}if(t.evaluater)return t.evaluater.evaluateExpression(l);pa(t)}function pa(t,e){let n=new _t("Cannot handle MDX estrees without `createEvaluater`",{ancestors:t.ancestors,place:e,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=t.filePath||void 0,n.url=kb+"#cannot-handle-mdx-estrees-without-createevaluater",n}function fw(t){let e={},n;for(n in t)Hf.call(t,n)&&(e[mw(n)]=t[n]);return e}function mw(t){let e=t.replace(Jk,pw);return e.slice(0,3)==="ms-"&&(e="-"+e),e}function pw(t){return"-"+t.toLowerCase()}var ha={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var Ii=Ie(Yi(),1),t0=Ie(lr(),1);var gw={};function Ll(t,e){let n=e||gw,l=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return Db(t,l,i)}function Db(t,e,n){if(yw(t)){if("value"in t)return t.type==="html"&&!n?"":t.value;if(e&&"alt"in t&&t.alt)return t.alt;if("children"in t)return Mb(t.children,e,n)}return Array.isArray(t)?Mb(t,e,n):""}function Mb(t,e,n){let l=[],i=-1;for(;++i<t.length;)l[i]=Db(t[i],e,n);return l.join("")}function yw(t){return!!(t&&typeof t=="object")}var Ob=document.createElement("i");function Vi(t){let e="&"+t+";";Ob.innerHTML=e;let n=Ob.textContent;return n.charCodeAt(n.length-1)===59&&t!=="semi"||n===e?!1:n}function Nt(t,e,n,l){let i=t.length,r=0,a;if(e<0?e=-e>i?0:i+e:e=e>i?i:e,n=n>0?n:0,l.length<1e4)a=Array.from(l),a.unshift(e,n),t.splice(...a);else for(n&&t.splice(e,n);r<l.length;)a=l.slice(r,r+1e4),a.unshift(e,0),t.splice(...a),r+=1e4,e+=1e4}function ue(t,e){return t.length>0?(Nt(t,t.length,0,e),t):e}var Rb={}.hasOwnProperty;function su(t){let e={},n=-1;for(;++n<t.length;)bw(e,t[n]);return e}function bw(t,e){let n;for(n in e){let i=(Rb.call(t,n)?t[n]:void 0)||(t[n]={}),r=e[n],a;if(r)for(a in r){Rb.call(i,a)||(i[a]=[]);let o=r[a];xw(i[a],Array.isArray(o)?o:o?[o]:[])}}}function xw(t,e){let n=-1,l=[];for(;++n<e.length;)(e[n].add==="after"?t:l).push(e[n]);Nt(t,0,0,l)}function fu(t,e){let n=Number.parseInt(t,e);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"\uFFFD":String.fromCodePoint(n)}function ne(t){return t.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Gt=al(/[A-Za-z]/),Mt=al(/[\dA-Za-z]/),_b=al(/[#-'*+\--9=?A-Z^-~]/);function Ul(t){return t!==null&&(t<32||t===127)}var da=al(/\d/),Nb=al(/[\dA-Fa-f]/),Lb=al(/[!-/:-@[-`{-~]/);function N(t){return t!==null&&t<-2}function P(t){return t!==null&&(t<0||t===32)}function V(t){return t===-2||t===-1||t===32}var Bl=al(new RegExp("\\p{P}|\\p{S}","u")),ln=al(/\s/);function al(t){return e;function e(n){return n!==null&&n>-1&&t.test(String.fromCharCode(n))}}function He(t){let e=[],n=-1,l=0,i=0;for(;++n<t.length;){let r=t.charCodeAt(n),a="";if(r===37&&Mt(t.charCodeAt(n+1))&&Mt(t.charCodeAt(n+2)))i=2;else if(r<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(r))||(a=String.fromCharCode(r));else if(r>55295&&r<57344){let o=t.charCodeAt(n+1);r<56320&&o>56319&&o<57344?(a=String.fromCharCode(r,o),i=1):a="\uFFFD"}else a=String.fromCharCode(r);a&&(e.push(t.slice(l,n),encodeURIComponent(a)),l=n+i+1,a=""),i&&(n+=i,i=0)}return e.join("")+t.slice(l)}function Y(t,e,n,l){let i=l?l-1:Number.POSITIVE_INFINITY,r=0;return a;function a(u){return V(u)?(t.enter(n),o(u)):e(u)}function o(u){return V(u)&&r++<i?(t.consume(u),o):(t.exit(n),e(u))}}var Ub={tokenize:vw};function vw(t){let e=t.attempt(this.parser.constructs.contentInitial,l,i),n;return e;function l(o){if(o===null){t.consume(o);return}return t.enter("lineEnding"),t.consume(o),t.exit("lineEnding"),Y(t,e,"linePrefix")}function i(o){return t.enter("paragraph"),r(o)}function r(o){let u=t.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=u),n=u,a(o)}function a(o){if(o===null){t.exit("chunkText"),t.exit("paragraph"),t.consume(o);return}return N(o)?(t.consume(o),t.exit("chunkText"),r):(t.consume(o),a)}}var Hb={tokenize:Sw},Bb={tokenize:kw};function Sw(t){let e=this,n=[],l=0,i,r,a;return o;function o(g){if(l<n.length){let S=n[l];return e.containerState=S[1],t.attempt(S[0].continuation,u,c)(g)}return c(g)}function u(g){if(l++,e.containerState._closeFlow){e.containerState._closeFlow=void 0,i&&d();let S=e.events.length,z=S,w;for(;z--;)if(e.events[z][0]==="exit"&&e.events[z][1].type==="chunkFlow"){w=e.events[z][1].end;break}h(l);let D=S;for(;D<e.events.length;)e.events[D][1].end=C({},w),D++;return Nt(e.events,z+1,0,e.events.slice(S)),e.events.length=D,c(g)}return o(g)}function c(g){if(l===n.length){if(!i)return p(g);if(i.currentConstruct&&i.currentConstruct.concrete)return y(g);e.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return e.containerState={},t.check(Bb,s,f)(g)}function s(g){return i&&d(),h(l),p(g)}function f(g){return e.parser.lazy[e.now().line]=l!==n.length,a=e.now().offset,y(g)}function p(g){return e.containerState={},t.attempt(Bb,m,y)(g)}function m(g){return l++,n.push([e.currentConstruct,e.containerState]),p(g)}function y(g){if(g===null){i&&d(),h(0),t.consume(g);return}return i=i||e.parser.flow(e.now()),t.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:r}),v(g)}function v(g){if(g===null){E(t.exit("chunkFlow"),!0),h(0),t.consume(g);return}return N(g)?(t.consume(g),E(t.exit("chunkFlow")),l=0,e.interrupt=void 0,o):(t.consume(g),v)}function E(g,S){let z=e.sliceStream(g);if(S&&z.push(null),g.previous=r,r&&(r.next=g),r=g,i.defineSkip(g.start),i.write(z),e.parser.lazy[g.start.line]){let w=i.events.length;for(;w--;)if(i.events[w][1].start.offset<a&&(!i.events[w][1].end||i.events[w][1].end.offset>a))return;let D=e.events.length,T=D,q,k;for(;T--;)if(e.events[T][0]==="exit"&&e.events[T][1].type==="chunkFlow"){if(q){k=e.events[T][1].end;break}q=!0}for(h(l),w=D;w<e.events.length;)e.events[w][1].end=C({},k),w++;Nt(e.events,T+1,0,e.events.slice(D)),e.events.length=w}}function h(g){let S=n.length;for(;S-- >g;){let z=n[S];e.containerState=z[1],z[0].exit.call(e,t)}n.length=g}function d(){i.write([null]),r=void 0,i=void 0,e.containerState._closeFlow=void 0}}function kw(t,e,n){return Y(t,t.attempt(this.parser.constructs.document,e,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function zn(t){if(t===null||P(t)||ln(t))return 1;if(Bl(t))return 2}function ol(t,e,n){let l=[],i=-1;for(;++i<t.length;){let r=t[i].resolveAll;r&&!l.includes(r)&&(e=r(e,n),l.push(r))}return e}var ga={name:"attention",resolveAll:ww,tokenize:Ew};function ww(t,e){let n=-1,l,i,r,a,o,u,c,s;for(;++n<t.length;)if(t[n][0]==="enter"&&t[n][1].type==="attentionSequence"&&t[n][1]._close){for(l=n;l--;)if(t[l][0]==="exit"&&t[l][1].type==="attentionSequence"&&t[l][1]._open&&e.sliceSerialize(t[l][1]).charCodeAt(0)===e.sliceSerialize(t[n][1]).charCodeAt(0)){if((t[l][1]._close||t[n][1]._open)&&(t[n][1].end.offset-t[n][1].start.offset)%3&&!((t[l][1].end.offset-t[l][1].start.offset+t[n][1].end.offset-t[n][1].start.offset)%3))continue;u=t[l][1].end.offset-t[l][1].start.offset>1&&t[n][1].end.offset-t[n][1].start.offset>1?2:1;let f=C({},t[l][1].end),p=C({},t[n][1].start);qb(f,-u),qb(p,u),a={type:u>1?"strongSequence":"emphasisSequence",start:f,end:C({},t[l][1].end)},o={type:u>1?"strongSequence":"emphasisSequence",start:C({},t[n][1].start),end:p},r={type:u>1?"strongText":"emphasisText",start:C({},t[l][1].end),end:C({},t[n][1].start)},i={type:u>1?"strong":"emphasis",start:C({},a.start),end:C({},o.end)},t[l][1].end=C({},a.start),t[n][1].start=C({},o.end),c=[],t[l][1].end.offset-t[l][1].start.offset&&(c=ue(c,[["enter",t[l][1],e],["exit",t[l][1],e]])),c=ue(c,[["enter",i,e],["enter",a,e],["exit",a,e],["enter",r,e]]),c=ue(c,ol(e.parser.constructs.insideSpan.null,t.slice(l+1,n),e)),c=ue(c,[["exit",r,e],["enter",o,e],["exit",o,e],["exit",i,e]]),t[n][1].end.offset-t[n][1].start.offset?(s=2,c=ue(c,[["enter",t[n][1],e],["exit",t[n][1],e]])):s=0,Nt(t,l-1,n-l+3,c),n=l+c.length-s-2;break}}for(n=-1;++n<t.length;)t[n][1].type==="attentionSequence"&&(t[n][1].type="data");return t}function Ew(t,e){let n=this.parser.constructs.attentionMarkers.null,l=this.previous,i=zn(l),r;return a;function a(u){return r=u,t.enter("attentionSequence"),o(u)}function o(u){if(u===r)return t.consume(u),o;let c=t.exit("attentionSequence"),s=zn(u),f=!s||s===2&&i||n.includes(u),p=!i||i===2&&s||n.includes(l);return c._open=!!(r===42?f:f&&(i||!p)),c._close=!!(r===42?p:p&&(s||!f)),e(u)}}function qb(t,e){t.column+=e,t.offset+=e,t._bufferIndex+=e}var Yf={name:"autolink",tokenize:Tw};function Tw(t,e,n){let l=0;return i;function i(m){return t.enter("autolink"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.enter("autolinkProtocol"),r}function r(m){return Gt(m)?(t.consume(m),a):m===64?n(m):c(m)}function a(m){return m===43||m===45||m===46||Mt(m)?(l=1,o(m)):c(m)}function o(m){return m===58?(t.consume(m),l=0,u):(m===43||m===45||m===46||Mt(m))&&l++<32?(t.consume(m),o):(l=0,c(m))}function u(m){return m===62?(t.exit("autolinkProtocol"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):m===null||m===32||m===60||Ul(m)?n(m):(t.consume(m),u)}function c(m){return m===64?(t.consume(m),s):_b(m)?(t.consume(m),c):n(m)}function s(m){return Mt(m)?f(m):n(m)}function f(m){return m===46?(t.consume(m),l=0,s):m===62?(t.exit("autolinkProtocol").type="autolinkEmail",t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):p(m)}function p(m){if((m===45||Mt(m))&&l++<63){let y=m===45?p:f;return t.consume(m),y}return n(m)}}var rn={partial:!0,tokenize:Aw};function Aw(t,e,n){return l;function l(r){return V(r)?Y(t,i,"linePrefix")(r):i(r)}function i(r){return r===null||N(r)?e(r):n(r)}}var mu={continuation:{tokenize:Cw},exit:Mw,name:"blockQuote",tokenize:zw};function zw(t,e,n){let l=this;return i;function i(a){if(a===62){let o=l.containerState;return o.open||(t.enter("blockQuote",{_container:!0}),o.open=!0),t.enter("blockQuotePrefix"),t.enter("blockQuoteMarker"),t.consume(a),t.exit("blockQuoteMarker"),r}return n(a)}function r(a){return V(a)?(t.enter("blockQuotePrefixWhitespace"),t.consume(a),t.exit("blockQuotePrefixWhitespace"),t.exit("blockQuotePrefix"),e):(t.exit("blockQuotePrefix"),e(a))}}function Cw(t,e,n){let l=this;return i;function i(a){return V(a)?Y(t,r,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):r(a)}function r(a){return t.attempt(mu,e,n)(a)}}function Mw(t){t.exit("blockQuote")}var pu={name:"characterEscape",tokenize:Dw};function Dw(t,e,n){return l;function l(r){return t.enter("characterEscape"),t.enter("escapeMarker"),t.consume(r),t.exit("escapeMarker"),i}function i(r){return Lb(r)?(t.enter("characterEscapeValue"),t.consume(r),t.exit("characterEscapeValue"),t.exit("characterEscape"),e):n(r)}}var hu={name:"characterReference",tokenize:Ow};function Ow(t,e,n){let l=this,i=0,r,a;return o;function o(f){return t.enter("characterReference"),t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),u}function u(f){return f===35?(t.enter("characterReferenceMarkerNumeric"),t.consume(f),t.exit("characterReferenceMarkerNumeric"),c):(t.enter("characterReferenceValue"),r=31,a=Mt,s(f))}function c(f){return f===88||f===120?(t.enter("characterReferenceMarkerHexadecimal"),t.consume(f),t.exit("characterReferenceMarkerHexadecimal"),t.enter("characterReferenceValue"),r=6,a=Nb,s):(t.enter("characterReferenceValue"),r=7,a=da,s(f))}function s(f){if(f===59&&i){let p=t.exit("characterReferenceValue");return a===Mt&&!Vi(l.sliceSerialize(p))?n(f):(t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),t.exit("characterReference"),e)}return a(f)&&i++<r?(t.consume(f),s):n(f)}}var jb={partial:!0,tokenize:_w},du={concrete:!0,name:"codeFenced",tokenize:Rw};function Rw(t,e,n){let l=this,i={partial:!0,tokenize:z},r=0,a=0,o;return u;function u(w){return c(w)}function c(w){let D=l.events[l.events.length-1];return r=D&&D[1].type==="linePrefix"?D[2].sliceSerialize(D[1],!0).length:0,o=w,t.enter("codeFenced"),t.enter("codeFencedFence"),t.enter("codeFencedFenceSequence"),s(w)}function s(w){return w===o?(a++,t.consume(w),s):a<3?n(w):(t.exit("codeFencedFenceSequence"),V(w)?Y(t,f,"whitespace")(w):f(w))}function f(w){return w===null||N(w)?(t.exit("codeFencedFence"),l.interrupt?e(w):t.check(jb,v,S)(w)):(t.enter("codeFencedFenceInfo"),t.enter("chunkString",{contentType:"string"}),p(w))}function p(w){return w===null||N(w)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),f(w)):V(w)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),Y(t,m,"whitespace")(w)):w===96&&w===o?n(w):(t.consume(w),p)}function m(w){return w===null||N(w)?f(w):(t.enter("codeFencedFenceMeta"),t.enter("chunkString",{contentType:"string"}),y(w))}function y(w){return w===null||N(w)?(t.exit("chunkString"),t.exit("codeFencedFenceMeta"),f(w)):w===96&&w===o?n(w):(t.consume(w),y)}function v(w){return t.attempt(i,S,E)(w)}function E(w){return t.enter("lineEnding"),t.consume(w),t.exit("lineEnding"),h}function h(w){return r>0&&V(w)?Y(t,d,"linePrefix",r+1)(w):d(w)}function d(w){return w===null||N(w)?t.check(jb,v,S)(w):(t.enter("codeFlowValue"),g(w))}function g(w){return w===null||N(w)?(t.exit("codeFlowValue"),d(w)):(t.consume(w),g)}function S(w){return t.exit("codeFenced"),e(w)}function z(w,D,T){let q=0;return k;function k(G){return w.enter("lineEnding"),w.consume(G),w.exit("lineEnding"),it}function it(G){return w.enter("codeFencedFence"),V(G)?Y(w,L,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(G):L(G)}function L(G){return G===o?(w.enter("codeFencedFenceSequence"),_(G)):T(G)}function _(G){return G===o?(q++,w.consume(G),_):q>=a?(w.exit("codeFencedFenceSequence"),V(G)?Y(w,j,"whitespace")(G):j(G)):T(G)}function j(G){return G===null||N(G)?(w.exit("codeFencedFence"),D(G)):T(G)}}}function _w(t,e,n){let l=this;return i;function i(a){return a===null?n(a):(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}var ya={name:"codeIndented",tokenize:Lw},Nw={partial:!0,tokenize:Uw};function Lw(t,e,n){let l=this;return i;function i(c){return t.enter("codeIndented"),Y(t,r,"linePrefix",5)(c)}function r(c){let s=l.events[l.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?a(c):n(c)}function a(c){return c===null?u(c):N(c)?t.attempt(Nw,a,u)(c):(t.enter("codeFlowValue"),o(c))}function o(c){return c===null||N(c)?(t.exit("codeFlowValue"),a(c)):(t.consume(c),o)}function u(c){return t.exit("codeIndented"),e(c)}}function Uw(t,e,n){let l=this;return i;function i(a){return l.parser.lazy[l.now().line]?n(a):N(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),i):Y(t,r,"linePrefix",5)(a)}function r(a){let o=l.events[l.events.length-1];return o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?e(a):N(a)?i(a):n(a)}}var Vf={name:"codeText",previous:Hw,resolve:Bw,tokenize:qw};function Bw(t){let e=t.length-4,n=3,l,i;if((t[n][1].type==="lineEnding"||t[n][1].type==="space")&&(t[e][1].type==="lineEnding"||t[e][1].type==="space")){for(l=n;++l<e;)if(t[l][1].type==="codeTextData"){t[n][1].type="codeTextPadding",t[e][1].type="codeTextPadding",n+=2,e-=2;break}}for(l=n-1,e++;++l<=e;)i===void 0?l!==e&&t[l][1].type!=="lineEnding"&&(i=l):(l===e||t[l][1].type==="lineEnding")&&(t[i][1].type="codeTextData",l!==i+2&&(t[i][1].end=t[l-1][1].end,t.splice(i+2,l-i-2),e-=l-i-2,l=i+2),i=void 0);return t}function Hw(t){return t!==96||this.events[this.events.length-1][1].type==="characterEscape"}function qw(t,e,n){let l=this,i=0,r,a;return o;function o(p){return t.enter("codeText"),t.enter("codeTextSequence"),u(p)}function u(p){return p===96?(t.consume(p),i++,u):(t.exit("codeTextSequence"),c(p))}function c(p){return p===null?n(p):p===32?(t.enter("space"),t.consume(p),t.exit("space"),c):p===96?(a=t.enter("codeTextSequence"),r=0,f(p)):N(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),c):(t.enter("codeTextData"),s(p))}function s(p){return p===null||p===32||p===96||N(p)?(t.exit("codeTextData"),c(p)):(t.consume(p),s)}function f(p){return p===96?(t.consume(p),r++,f):r===i?(t.exit("codeTextSequence"),t.exit("codeText"),e(p)):(a.type="codeTextData",s(p))}}var gu=class{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){let l=n==null?Number.POSITIVE_INFINITY:n;return l<this.left.length?this.left.slice(e,l):e>this.left.length?this.right.slice(this.right.length-l+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-l+this.left.length).reverse())}splice(e,n,l){let i=n||0;this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return l&&ba(this.left,l),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ba(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ba(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&this.right.length===0||e<0&&this.left.length===0))if(e<this.left.length){let n=this.left.splice(e,Number.POSITIVE_INFINITY);ba(this.right,n.reverse())}else{let n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ba(this.left,n.reverse())}}};function ba(t,e){let n=0;if(e.length<1e4)t.push(...e);else for(;n<e.length;)t.push(...e.slice(n,n+1e4)),n+=1e4}function yu(t){let e={},n=-1,l,i,r,a,o,u,c,s=new gu(t);for(;++n<s.length;){for(;n in e;)n=e[n];if(l=s.get(n),n&&l[1].type==="chunkFlow"&&s.get(n-1)[1].type==="listItemPrefix"&&(u=l[1]._tokenizer.events,r=0,r<u.length&&u[r][1].type==="lineEndingBlank"&&(r+=2),r<u.length&&u[r][1].type==="content"))for(;++r<u.length&&u[r][1].type!=="content";)u[r][1].type==="chunkText"&&(u[r][1]._isInFirstContentOfListItem=!0,r++);if(l[0]==="enter")l[1].contentType&&(Object.assign(e,jw(s,n)),n=e[n],c=!0);else if(l[1]._container){for(r=n,i=void 0;r--;)if(a=s.get(r),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(i&&(s.get(i)[1].type="lineEndingBlank"),a[1].type="lineEnding",i=r);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;i&&(l[1].end=C({},s.get(i)[1].start),o=s.slice(i,n),o.unshift(l),s.splice(i,n-i+1,o))}}return Nt(t,0,Number.POSITIVE_INFINITY,s.slice(0)),!c}function jw(t,e){let n=t.get(e)[1],l=t.get(e)[2],i=e-1,r=[],a=n._tokenizer;a||(a=l.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));let o=a.events,u=[],c={},s,f,p=-1,m=n,y=0,v=0,E=[v];for(;m;){for(;t.get(++i)[1]!==m;);r.push(i),m._tokenizer||(s=l.sliceStream(m),m.next||s.push(null),f&&a.defineSkip(m.start),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(s),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=m,m=m.next}for(m=n;++p<o.length;)o[p][0]==="exit"&&o[p-1][0]==="enter"&&o[p][1].type===o[p-1][1].type&&o[p][1].start.line!==o[p][1].end.line&&(v=p+1,E.push(v),m._tokenizer=void 0,m.previous=void 0,m=m.next);for(a.events=[],m?(m._tokenizer=void 0,m.previous=void 0):E.pop(),p=E.length;p--;){let h=o.slice(E[p],E[p+1]),d=r.pop();u.push([d,d+h.length-1]),t.splice(d,2,h)}for(u.reverse(),p=-1;++p<u.length;)c[y+u[p][0]]=y+u[p][1],y+=u[p][1]-u[p][0]-1;return c}var Xf={resolve:Yw,tokenize:Vw},Gw={partial:!0,tokenize:Xw};function Yw(t){return yu(t),t}function Vw(t,e){let n;return l;function l(o){return t.enter("content"),n=t.enter("chunkContent",{contentType:"content"}),i(o)}function i(o){return o===null?r(o):N(o)?t.check(Gw,a,r)(o):(t.consume(o),i)}function r(o){return t.exit("chunkContent"),t.exit("content"),e(o)}function a(o){return t.consume(o),t.exit("chunkContent"),n.next=t.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function Xw(t,e,n){let l=this;return i;function i(a){return t.exit("chunkContent"),t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),Y(t,r,"linePrefix")}function r(a){if(a===null||N(a))return n(a);let o=l.events[l.events.length-1];return!l.parser.constructs.disable.null.includes("codeIndented")&&o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?e(a):t.interrupt(l.parser.constructs.flow,n,e)(a)}}function bu(t,e,n,l,i,r,a,o,u){let c=u||Number.POSITIVE_INFINITY,s=0;return f;function f(h){return h===60?(t.enter(l),t.enter(i),t.enter(r),t.consume(h),t.exit(r),p):h===null||h===32||h===41||Ul(h)?n(h):(t.enter(l),t.enter(a),t.enter(o),t.enter("chunkString",{contentType:"string"}),v(h))}function p(h){return h===62?(t.enter(r),t.consume(h),t.exit(r),t.exit(i),t.exit(l),e):(t.enter(o),t.enter("chunkString",{contentType:"string"}),m(h))}function m(h){return h===62?(t.exit("chunkString"),t.exit(o),p(h)):h===null||h===60||N(h)?n(h):(t.consume(h),h===92?y:m)}function y(h){return h===60||h===62||h===92?(t.consume(h),m):m(h)}function v(h){return!s&&(h===null||h===41||P(h))?(t.exit("chunkString"),t.exit(o),t.exit(a),t.exit(l),e(h)):s<c&&h===40?(t.consume(h),s++,v):h===41?(t.consume(h),s--,v):h===null||h===32||h===40||Ul(h)?n(h):(t.consume(h),h===92?E:v)}function E(h){return h===40||h===41||h===92?(t.consume(h),v):v(h)}}function xu(t,e,n,l,i,r){let a=this,o=0,u;return c;function c(m){return t.enter(l),t.enter(i),t.consume(m),t.exit(i),t.enter(r),s}function s(m){return o>999||m===null||m===91||m===93&&!u||m===94&&!o&&"_hiddenFootnoteSupport"in a.parser.constructs?n(m):m===93?(t.exit(r),t.enter(i),t.consume(m),t.exit(i),t.exit(l),e):N(m)?(t.enter("lineEnding"),t.consume(m),t.exit("lineEnding"),s):(t.enter("chunkString",{contentType:"string"}),f(m))}function f(m){return m===null||m===91||m===93||N(m)||o++>999?(t.exit("chunkString"),s(m)):(t.consume(m),u||(u=!V(m)),m===92?p:f)}function p(m){return m===91||m===92||m===93?(t.consume(m),o++,f):f(m)}}function vu(t,e,n,l,i,r){let a;return o;function o(p){return p===34||p===39||p===40?(t.enter(l),t.enter(i),t.consume(p),t.exit(i),a=p===40?41:p,u):n(p)}function u(p){return p===a?(t.enter(i),t.consume(p),t.exit(i),t.exit(l),e):(t.enter(r),c(p))}function c(p){return p===a?(t.exit(r),u(a)):p===null?n(p):N(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),Y(t,c,"linePrefix")):(t.enter("chunkString",{contentType:"string"}),s(p))}function s(p){return p===a||p===null||N(p)?(t.exit("chunkString"),c(p)):(t.consume(p),p===92?f:s)}function f(p){return p===a||p===92?(t.consume(p),s):s(p)}}function Hl(t,e){let n;return l;function l(i){return N(i)?(t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),n=!0,l):V(i)?Y(t,l,n?"linePrefix":"lineSuffix")(i):e(i)}}var Qf={name:"definition",tokenize:Zw},Qw={partial:!0,tokenize:Iw};function Zw(t,e,n){let l=this,i;return r;function r(m){return t.enter("definition"),a(m)}function a(m){return xu.call(l,t,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(m)}function o(m){return i=ne(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),m===58?(t.enter("definitionMarker"),t.consume(m),t.exit("definitionMarker"),u):n(m)}function u(m){return P(m)?Hl(t,c)(m):c(m)}function c(m){return bu(t,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(m)}function s(m){return t.attempt(Qw,f,f)(m)}function f(m){return V(m)?Y(t,p,"whitespace")(m):p(m)}function p(m){return m===null||N(m)?(t.exit("definition"),l.parser.defined.push(i),e(m)):n(m)}}function Iw(t,e,n){return l;function l(o){return P(o)?Hl(t,i)(o):n(o)}function i(o){return vu(t,r,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(o)}function r(o){return V(o)?Y(t,a,"whitespace")(o):a(o)}function a(o){return o===null||N(o)?e(o):n(o)}}var Zf={name:"hardBreakEscape",tokenize:Fw};function Fw(t,e,n){return l;function l(r){return t.enter("hardBreakEscape"),t.consume(r),i}function i(r){return N(r)?(t.exit("hardBreakEscape"),e(r)):n(r)}}var If={name:"headingAtx",resolve:Kw,tokenize:Jw};function Kw(t,e){let n=t.length-2,l=3,i,r;return t[l][1].type==="whitespace"&&(l+=2),n-2>l&&t[n][1].type==="whitespace"&&(n-=2),t[n][1].type==="atxHeadingSequence"&&(l===n-1||n-4>l&&t[n-2][1].type==="whitespace")&&(n-=l+1===n?2:4),n>l&&(i={type:"atxHeadingText",start:t[l][1].start,end:t[n][1].end},r={type:"chunkText",start:t[l][1].start,end:t[n][1].end,contentType:"text"},Nt(t,l,n-l+1,[["enter",i,e],["enter",r,e],["exit",r,e],["exit",i,e]])),t}function Jw(t,e,n){let l=0;return i;function i(s){return t.enter("atxHeading"),r(s)}function r(s){return t.enter("atxHeadingSequence"),a(s)}function a(s){return s===35&&l++<6?(t.consume(s),a):s===null||P(s)?(t.exit("atxHeadingSequence"),o(s)):n(s)}function o(s){return s===35?(t.enter("atxHeadingSequence"),u(s)):s===null||N(s)?(t.exit("atxHeading"),e(s)):V(s)?Y(t,o,"whitespace")(s):(t.enter("atxHeadingText"),c(s))}function u(s){return s===35?(t.consume(s),u):(t.exit("atxHeadingSequence"),o(s))}function c(s){return s===null||s===35||P(s)?(t.exit("atxHeadingText"),o(s)):(t.consume(s),c)}}var Gb=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ff=["pre","script","style","textarea"];var Kf={concrete:!0,name:"htmlFlow",resolveTo:$w,tokenize:tE},Ww={partial:!0,tokenize:nE},Pw={partial:!0,tokenize:eE};function $w(t){let e=t.length;for(;e--&&!(t[e][0]==="enter"&&t[e][1].type==="htmlFlow"););return e>1&&t[e-2][1].type==="linePrefix"&&(t[e][1].start=t[e-2][1].start,t[e+1][1].start=t[e-2][1].start,t.splice(e-2,2)),t}function tE(t,e,n){let l=this,i,r,a,o,u;return c;function c(b){return s(b)}function s(b){return t.enter("htmlFlow"),t.enter("htmlFlowData"),t.consume(b),f}function f(b){return b===33?(t.consume(b),p):b===47?(t.consume(b),r=!0,v):b===63?(t.consume(b),i=3,l.interrupt?e:x):Gt(b)?(t.consume(b),a=String.fromCharCode(b),E):n(b)}function p(b){return b===45?(t.consume(b),i=2,m):b===91?(t.consume(b),i=5,o=0,y):Gt(b)?(t.consume(b),i=4,l.interrupt?e:x):n(b)}function m(b){return b===45?(t.consume(b),l.interrupt?e:x):n(b)}function y(b){let rt="CDATA[";return b===rt.charCodeAt(o++)?(t.consume(b),o===rt.length?l.interrupt?e:L:y):n(b)}function v(b){return Gt(b)?(t.consume(b),a=String.fromCharCode(b),E):n(b)}function E(b){if(b===null||b===47||b===62||P(b)){let rt=b===47,Lt=a.toLowerCase();return!rt&&!r&&Ff.includes(Lt)?(i=1,l.interrupt?e(b):L(b)):Gb.includes(a.toLowerCase())?(i=6,rt?(t.consume(b),h):l.interrupt?e(b):L(b)):(i=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(b):r?d(b):g(b))}return b===45||Mt(b)?(t.consume(b),a+=String.fromCharCode(b),E):n(b)}function h(b){return b===62?(t.consume(b),l.interrupt?e:L):n(b)}function d(b){return V(b)?(t.consume(b),d):k(b)}function g(b){return b===47?(t.consume(b),k):b===58||b===95||Gt(b)?(t.consume(b),S):V(b)?(t.consume(b),g):k(b)}function S(b){return b===45||b===46||b===58||b===95||Mt(b)?(t.consume(b),S):z(b)}function z(b){return b===61?(t.consume(b),w):V(b)?(t.consume(b),z):g(b)}function w(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(t.consume(b),u=b,D):V(b)?(t.consume(b),w):T(b)}function D(b){return b===u?(t.consume(b),u=null,q):b===null||N(b)?n(b):(t.consume(b),D)}function T(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||P(b)?z(b):(t.consume(b),T)}function q(b){return b===47||b===62||V(b)?g(b):n(b)}function k(b){return b===62?(t.consume(b),it):n(b)}function it(b){return b===null||N(b)?L(b):V(b)?(t.consume(b),it):n(b)}function L(b){return b===45&&i===2?(t.consume(b),X):b===60&&i===1?(t.consume(b),lt):b===62&&i===4?(t.consume(b),Dt):b===63&&i===3?(t.consume(b),x):b===93&&i===5?(t.consume(b),Kt):N(b)&&(i===6||i===7)?(t.exit("htmlFlowData"),t.check(Ww,ce,_)(b)):b===null||N(b)?(t.exit("htmlFlowData"),_(b)):(t.consume(b),L)}function _(b){return t.check(Pw,j,ce)(b)}function j(b){return t.enter("lineEnding"),t.consume(b),t.exit("lineEnding"),G}function G(b){return b===null||N(b)?_(b):(t.enter("htmlFlowData"),L(b))}function X(b){return b===45?(t.consume(b),x):L(b)}function lt(b){return b===47?(t.consume(b),a="",U):L(b)}function U(b){if(b===62){let rt=a.toLowerCase();return Ff.includes(rt)?(t.consume(b),Dt):L(b)}return Gt(b)&&a.length<8?(t.consume(b),a+=String.fromCharCode(b),U):L(b)}function Kt(b){return b===93?(t.consume(b),x):L(b)}function x(b){return b===62?(t.consume(b),Dt):b===45&&i===2?(t.consume(b),x):L(b)}function Dt(b){return b===null||N(b)?(t.exit("htmlFlowData"),ce(b)):(t.consume(b),Dt)}function ce(b){return t.exit("htmlFlow"),e(b)}}function eE(t,e,n){let l=this;return i;function i(a){return N(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r):n(a)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}function nE(t,e,n){return l;function l(i){return t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),t.attempt(rn,e,n)}}var Jf={name:"htmlText",tokenize:lE};function lE(t,e,n){let l=this,i,r,a;return o;function o(x){return t.enter("htmlText"),t.enter("htmlTextData"),t.consume(x),u}function u(x){return x===33?(t.consume(x),c):x===47?(t.consume(x),z):x===63?(t.consume(x),g):Gt(x)?(t.consume(x),T):n(x)}function c(x){return x===45?(t.consume(x),s):x===91?(t.consume(x),r=0,y):Gt(x)?(t.consume(x),d):n(x)}function s(x){return x===45?(t.consume(x),m):n(x)}function f(x){return x===null?n(x):x===45?(t.consume(x),p):N(x)?(a=f,lt(x)):(t.consume(x),f)}function p(x){return x===45?(t.consume(x),m):f(x)}function m(x){return x===62?X(x):x===45?p(x):f(x)}function y(x){let Dt="CDATA[";return x===Dt.charCodeAt(r++)?(t.consume(x),r===Dt.length?v:y):n(x)}function v(x){return x===null?n(x):x===93?(t.consume(x),E):N(x)?(a=v,lt(x)):(t.consume(x),v)}function E(x){return x===93?(t.consume(x),h):v(x)}function h(x){return x===62?X(x):x===93?(t.consume(x),h):v(x)}function d(x){return x===null||x===62?X(x):N(x)?(a=d,lt(x)):(t.consume(x),d)}function g(x){return x===null?n(x):x===63?(t.consume(x),S):N(x)?(a=g,lt(x)):(t.consume(x),g)}function S(x){return x===62?X(x):g(x)}function z(x){return Gt(x)?(t.consume(x),w):n(x)}function w(x){return x===45||Mt(x)?(t.consume(x),w):D(x)}function D(x){return N(x)?(a=D,lt(x)):V(x)?(t.consume(x),D):X(x)}function T(x){return x===45||Mt(x)?(t.consume(x),T):x===47||x===62||P(x)?q(x):n(x)}function q(x){return x===47?(t.consume(x),X):x===58||x===95||Gt(x)?(t.consume(x),k):N(x)?(a=q,lt(x)):V(x)?(t.consume(x),q):X(x)}function k(x){return x===45||x===46||x===58||x===95||Mt(x)?(t.consume(x),k):it(x)}function it(x){return x===61?(t.consume(x),L):N(x)?(a=it,lt(x)):V(x)?(t.consume(x),it):q(x)}function L(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(t.consume(x),i=x,_):N(x)?(a=L,lt(x)):V(x)?(t.consume(x),L):(t.consume(x),j)}function _(x){return x===i?(t.consume(x),i=void 0,G):x===null?n(x):N(x)?(a=_,lt(x)):(t.consume(x),_)}function j(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||P(x)?q(x):(t.consume(x),j)}function G(x){return x===47||x===62||P(x)?q(x):n(x)}function X(x){return x===62?(t.consume(x),t.exit("htmlTextData"),t.exit("htmlText"),e):n(x)}function lt(x){return t.exit("htmlTextData"),t.enter("lineEnding"),t.consume(x),t.exit("lineEnding"),U}function U(x){return V(x)?Y(t,Kt,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):Kt(x)}function Kt(x){return t.enter("htmlTextData"),a(x)}}var ql={name:"labelEnd",resolveAll:oE,resolveTo:uE,tokenize:cE},iE={tokenize:sE},rE={tokenize:fE},aE={tokenize:mE};function oE(t){let e=-1,n=[];for(;++e<t.length;){let l=t[e][1];if(n.push(t[e]),l.type==="labelImage"||l.type==="labelLink"||l.type==="labelEnd"){let i=l.type==="labelImage"?4:2;l.type="data",e+=i}}return t.length!==n.length&&Nt(t,0,t.length,n),t}function uE(t,e){let n=t.length,l=0,i,r,a,o;for(;n--;)if(i=t[n][1],r){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;t[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(t[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(r=n,i.type!=="labelLink")){l=2;break}}else i.type==="labelEnd"&&(a=n);let u={type:t[r][1].type==="labelLink"?"link":"image",start:C({},t[r][1].start),end:C({},t[t.length-1][1].end)},c={type:"label",start:C({},t[r][1].start),end:C({},t[a][1].end)},s={type:"labelText",start:C({},t[r+l+2][1].end),end:C({},t[a-2][1].start)};return o=[["enter",u,e],["enter",c,e]],o=ue(o,t.slice(r+1,r+l+3)),o=ue(o,[["enter",s,e]]),o=ue(o,ol(e.parser.constructs.insideSpan.null,t.slice(r+l+4,a-3),e)),o=ue(o,[["exit",s,e],t[a-2],t[a-1],["exit",c,e]]),o=ue(o,t.slice(a+1)),o=ue(o,[["exit",u,e]]),Nt(t,r,t.length,o),t}function cE(t,e,n){let l=this,i=l.events.length,r,a;for(;i--;)if((l.events[i][1].type==="labelImage"||l.events[i][1].type==="labelLink")&&!l.events[i][1]._balanced){r=l.events[i][1];break}return o;function o(p){return r?r._inactive?f(p):(a=l.parser.defined.includes(ne(l.sliceSerialize({start:r.end,end:l.now()}))),t.enter("labelEnd"),t.enter("labelMarker"),t.consume(p),t.exit("labelMarker"),t.exit("labelEnd"),u):n(p)}function u(p){return p===40?t.attempt(iE,s,a?s:f)(p):p===91?t.attempt(rE,s,a?c:f)(p):a?s(p):f(p)}function c(p){return t.attempt(aE,s,f)(p)}function s(p){return e(p)}function f(p){return r._balanced=!0,n(p)}}function sE(t,e,n){return l;function l(f){return t.enter("resource"),t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),i}function i(f){return P(f)?Hl(t,r)(f):r(f)}function r(f){return f===41?s(f):bu(t,a,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function a(f){return P(f)?Hl(t,u)(f):s(f)}function o(f){return n(f)}function u(f){return f===34||f===39||f===40?vu(t,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):s(f)}function c(f){return P(f)?Hl(t,s)(f):s(f)}function s(f){return f===41?(t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),t.exit("resource"),e):n(f)}}function fE(t,e,n){let l=this;return i;function i(o){return xu.call(l,t,r,a,"reference","referenceMarker","referenceString")(o)}function r(o){return l.parser.defined.includes(ne(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)))?e(o):n(o)}function a(o){return n(o)}}function mE(t,e,n){return l;function l(r){return t.enter("reference"),t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),i}function i(r){return r===93?(t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),t.exit("reference"),e):n(r)}}var Wf={name:"labelStartImage",resolveAll:ql.resolveAll,tokenize:pE};function pE(t,e,n){let l=this;return i;function i(o){return t.enter("labelImage"),t.enter("labelImageMarker"),t.consume(o),t.exit("labelImageMarker"),r}function r(o){return o===91?(t.enter("labelMarker"),t.consume(o),t.exit("labelMarker"),t.exit("labelImage"),a):n(o)}function a(o){return o===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(o):e(o)}}var Pf={name:"labelStartLink",resolveAll:ql.resolveAll,tokenize:hE};function hE(t,e,n){let l=this;return i;function i(a){return t.enter("labelLink"),t.enter("labelMarker"),t.consume(a),t.exit("labelMarker"),t.exit("labelLink"),r}function r(a){return a===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(a):e(a)}}var xa={name:"lineEnding",tokenize:dE};function dE(t,e){return n;function n(l){return t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),Y(t,e,"linePrefix")}}var jl={name:"thematicBreak",tokenize:gE};function gE(t,e,n){let l=0,i;return r;function r(c){return t.enter("thematicBreak"),a(c)}function a(c){return i=c,o(c)}function o(c){return c===i?(t.enter("thematicBreakSequence"),u(c)):l>=3&&(c===null||N(c))?(t.exit("thematicBreak"),e(c)):n(c)}function u(c){return c===i?(t.consume(c),l++,u):(t.exit("thematicBreakSequence"),V(c)?Y(t,o,"whitespace")(c):o(c))}}var le={continuation:{tokenize:vE},exit:kE,name:"list",tokenize:xE},yE={partial:!0,tokenize:wE},bE={partial:!0,tokenize:SE};function xE(t,e,n){let l=this,i=l.events[l.events.length-1],r=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return o;function o(m){let y=l.containerState.type||(m===42||m===43||m===45?"listUnordered":"listOrdered");if(y==="listUnordered"?!l.containerState.marker||m===l.containerState.marker:da(m)){if(l.containerState.type||(l.containerState.type=y,t.enter(y,{_container:!0})),y==="listUnordered")return t.enter("listItemPrefix"),m===42||m===45?t.check(jl,n,c)(m):c(m);if(!l.interrupt||m===49)return t.enter("listItemPrefix"),t.enter("listItemValue"),u(m)}return n(m)}function u(m){return da(m)&&++a<10?(t.consume(m),u):(!l.interrupt||a<2)&&(l.containerState.marker?m===l.containerState.marker:m===41||m===46)?(t.exit("listItemValue"),c(m)):n(m)}function c(m){return t.enter("listItemMarker"),t.consume(m),t.exit("listItemMarker"),l.containerState.marker=l.containerState.marker||m,t.check(rn,l.interrupt?n:s,t.attempt(yE,p,f))}function s(m){return l.containerState.initialBlankLine=!0,r++,p(m)}function f(m){return V(m)?(t.enter("listItemPrefixWhitespace"),t.consume(m),t.exit("listItemPrefixWhitespace"),p):n(m)}function p(m){return l.containerState.size=r+l.sliceSerialize(t.exit("listItemPrefix"),!0).length,e(m)}}function vE(t,e,n){let l=this;return l.containerState._closeFlow=void 0,t.check(rn,i,r);function i(o){return l.containerState.furtherBlankLines=l.containerState.furtherBlankLines||l.containerState.initialBlankLine,Y(t,e,"listItemIndent",l.containerState.size+1)(o)}function r(o){return l.containerState.furtherBlankLines||!V(o)?(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,a(o)):(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,t.attempt(bE,e,a)(o))}function a(o){return l.containerState._closeFlow=!0,l.interrupt=void 0,Y(t,t.attempt(le,e,n),"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}function SE(t,e,n){let l=this;return Y(t,i,"listItemIndent",l.containerState.size+1);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===l.containerState.size?e(r):n(r)}}function kE(t){t.exit(this.containerState.type)}function wE(t,e,n){let l=this;return Y(t,i,"listItemPrefixWhitespace",l.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(r){let a=l.events[l.events.length-1];return!V(r)&&a&&a[1].type==="listItemPrefixWhitespace"?e(r):n(r)}}var Su={name:"setextUnderline",resolveTo:EE,tokenize:TE};function EE(t,e){let n=t.length,l,i,r;for(;n--;)if(t[n][0]==="enter"){if(t[n][1].type==="content"){l=n;break}t[n][1].type==="paragraph"&&(i=n)}else t[n][1].type==="content"&&t.splice(n,1),!r&&t[n][1].type==="definition"&&(r=n);let a={type:"setextHeading",start:C({},t[l][1].start),end:C({},t[t.length-1][1].end)};return t[i][1].type="setextHeadingText",r?(t.splice(i,0,["enter",a,e]),t.splice(r+1,0,["exit",t[l][1],e]),t[l][1].end=C({},t[r][1].end)):t[l][1]=a,t.push(["exit",a,e]),t}function TE(t,e,n){let l=this,i;return r;function r(c){let s=l.events.length,f;for(;s--;)if(l.events[s][1].type!=="lineEnding"&&l.events[s][1].type!=="linePrefix"&&l.events[s][1].type!=="content"){f=l.events[s][1].type==="paragraph";break}return!l.parser.lazy[l.now().line]&&(l.interrupt||f)?(t.enter("setextHeadingLine"),i=c,a(c)):n(c)}function a(c){return t.enter("setextHeadingLineSequence"),o(c)}function o(c){return c===i?(t.consume(c),o):(t.exit("setextHeadingLineSequence"),V(c)?Y(t,u,"lineSuffix")(c):u(c))}function u(c){return c===null||N(c)?(t.exit("setextHeadingLine"),e(c)):n(c)}}var Yb={tokenize:AE};function AE(t){let e=this,n=t.attempt(rn,l,t.attempt(this.parser.constructs.flowInitial,i,Y(t,t.attempt(this.parser.constructs.flow,i,t.attempt(Xf,i)),"linePrefix")));return n;function l(r){if(r===null){t.consume(r);return}return t.enter("lineEndingBlank"),t.consume(r),t.exit("lineEndingBlank"),e.currentConstruct=void 0,n}function i(r){if(r===null){t.consume(r);return}return t.enter("lineEnding"),t.consume(r),t.exit("lineEnding"),e.currentConstruct=void 0,n}}var Vb={resolveAll:Ib()},Xb=Zb("string"),Qb=Zb("text");function Zb(t){return{resolveAll:Ib(t==="text"?zE:void 0),tokenize:e};function e(n){let l=this,i=this.parser.constructs[t],r=n.attempt(i,a,o);return a;function a(s){return c(s)?r(s):o(s)}function o(s){if(s===null){n.consume(s);return}return n.enter("data"),n.consume(s),u}function u(s){return c(s)?(n.exit("data"),r(s)):(n.consume(s),u)}function c(s){if(s===null)return!0;let f=i[s],p=-1;if(f)for(;++p<f.length;){let m=f[p];if(!m.previous||m.previous.call(l,l.previous))return!0}return!1}}}function Ib(t){return e;function e(n,l){let i=-1,r;for(;++i<=n.length;)r===void 0?n[i]&&n[i][1].type==="data"&&(r=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return t?t(n,l):n}}function zE(t,e){let n=0;for(;++n<=t.length;)if((n===t.length||t[n][1].type==="lineEnding")&&t[n-1][1].type==="data"){let l=t[n-1][1],i=e.sliceStream(l),r=i.length,a=-1,o=0,u;for(;r--;){let c=i[r];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)o++,a--;if(a)break;a=-1}else if(c===-2)u=!0,o++;else if(c!==-1){r++;break}}if(e._contentTypeTextTrailing&&n===t.length&&(o=0),o){let c={type:n===t.length||u||o<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:r?a:l.start._bufferIndex+a,_index:l.start._index+r,line:l.end.line,column:l.end.column-o,offset:l.end.offset-o},end:C({},l.end)};l.end=C({},c.start),l.start.offset===l.end.offset?Object.assign(l,c):(t.splice(n,0,["enter",c,e],["exit",c,e]),n+=2)}n++}return t}var $f={};yp($f,{attentionMarkers:()=>LE,contentInitial:()=>ME,disable:()=>UE,document:()=>CE,flow:()=>OE,flowInitial:()=>DE,insideSpan:()=>NE,string:()=>RE,text:()=>_E});var CE={42:le,43:le,45:le,48:le,49:le,50:le,51:le,52:le,53:le,54:le,55:le,56:le,57:le,62:mu},ME={91:Qf},DE={[-2]:ya,[-1]:ya,32:ya},OE={35:If,42:jl,45:[Su,jl],60:Kf,61:Su,95:jl,96:du,126:du},RE={38:hu,92:pu},_E={[-5]:xa,[-4]:xa,[-3]:xa,33:Wf,38:hu,42:ga,60:[Yf,Jf],91:Pf,92:[Zf,pu],93:ql,95:ga,96:Vf},NE={null:[ga,Vb]},LE={null:[42,95]},UE={null:[]};function Fb(t,e,n){let l={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},r=[],a=[],o=[],u=!0,c={attempt:q(D),check:q(T),consume:S,enter:z,exit:w,interrupt:q(T,{interrupt:!0})},s={code:null,containerState:{},defineSkip:h,events:[],now:E,parser:t,previous:null,sliceSerialize:y,sliceStream:v,write:m},f=e.tokenize.call(s,c),p;return e.resolveAll&&r.push(e),s;function m(_){return a=ue(a,_),d(),a[a.length-1]!==null?[]:(k(e,0),s.events=ol(r,s.events,s),s.events)}function y(_,j){return HE(v(_),j)}function v(_){return BE(a,_)}function E(){let{_bufferIndex:_,_index:j,line:G,column:X,offset:lt}=l;return{_bufferIndex:_,_index:j,line:G,column:X,offset:lt}}function h(_){i[_.line]=_.column,L()}function d(){let _;for(;l._index<a.length;){let j=a[l._index];if(typeof j=="string")for(_=l._index,l._bufferIndex<0&&(l._bufferIndex=0);l._index===_&&l._bufferIndex<j.length;)g(j.charCodeAt(l._bufferIndex));else g(j)}}function g(_){u=void 0,p=_,f=f(_)}function S(_){N(_)?(l.line++,l.column=1,l.offset+=_===-3?2:1,L()):_!==-1&&(l.column++,l.offset++),l._bufferIndex<0?l._index++:(l._bufferIndex++,l._bufferIndex===a[l._index].length&&(l._bufferIndex=-1,l._index++)),s.previous=_,u=!0}function z(_,j){let G=j||{};return G.type=_,G.start=E(),s.events.push(["enter",G,s]),o.push(G),G}function w(_){let j=o.pop();return j.end=E(),s.events.push(["exit",j,s]),j}function D(_,j){k(_,j.from)}function T(_,j){j.restore()}function q(_,j){return G;function G(X,lt,U){let Kt,x,Dt,ce;return Array.isArray(X)?rt(X):"tokenize"in X?rt([X]):b(X);function b(bt){return Ze;function Ze(se){let qe=se!==null&&bt[se],je=se!==null&&bt.null,Gu=[...Array.isArray(qe)?qe:qe?[qe]:[],...Array.isArray(je)?je:je?[je]:[]];return rt(Gu)(se)}}function rt(bt){return Kt=bt,x=0,bt.length===0?U:Lt(bt[x])}function Lt(bt){return Ze;function Ze(se){return ce=it(),Dt=bt,bt.partial||(s.currentConstruct=bt),bt.name&&s.parser.constructs.disable.null.includes(bt.name)?gt(se):bt.tokenize.call(j?Object.assign(Object.create(s),j):s,c,Dn,gt)(se)}}function Dn(bt){return u=!0,_(Dt,ce),lt}function gt(bt){return u=!0,ce.restore(),++x<Kt.length?Lt(Kt[x]):U}}}function k(_,j){_.resolveAll&&!r.includes(_)&&r.push(_),_.resolve&&Nt(s.events,j,s.events.length-j,_.resolve(s.events.slice(j),s)),_.resolveTo&&(s.events=_.resolveTo(s.events,s))}function it(){let _=E(),j=s.previous,G=s.currentConstruct,X=s.events.length,lt=Array.from(o);return{from:X,restore:U};function U(){l=_,s.previous=j,s.currentConstruct=G,s.events.length=X,o=lt,L()}}function L(){l.line in i&&l.column<2&&(l.column=i[l.line],l.offset+=i[l.line]-1)}}function BE(t,e){let n=e.start._index,l=e.start._bufferIndex,i=e.end._index,r=e.end._bufferIndex,a;if(n===i)a=[t[n].slice(l,r)];else{if(a=t.slice(n,i),l>-1){let o=a[0];typeof o=="string"?a[0]=o.slice(l):a.shift()}r>0&&a.push(t[i].slice(0,r))}return a}function HE(t,e){let n=-1,l=[],i;for(;++n<t.length;){let r=t[n],a;if(typeof r=="string")a=r;else switch(r){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=e?" ":"	";break}case-1:{if(!e&&i)continue;a=" ";break}default:a=String.fromCharCode(r)}i=r===-2,l.push(a)}return l.join("")}function tm(t){let l={constructs:su([$f,...(t||{}).extensions||[]]),content:i(Ub),defined:[],document:i(Hb),flow:i(Yb),lazy:{},string:i(Xb),text:i(Qb)};return l;function i(r){return a;function a(o){return Fb(l,r,o)}}}function em(t){for(;!yu(t););return t}var Kb=/[\0\t\n\r]/g;function nm(){let t=1,e="",n=!0,l;return i;function i(r,a,o){let u=[],c,s,f,p,m;for(r=e+(typeof r=="string"?r.toString():new TextDecoder(a||void 0).decode(r)),f=0,e="",n&&(r.charCodeAt(0)===65279&&f++,n=void 0);f<r.length;){if(Kb.lastIndex=f,c=Kb.exec(r),p=c&&c.index!==void 0?c.index:r.length,m=r.charCodeAt(p),!c){e=r.slice(f);break}if(m===10&&f===p&&l)u.push(-3),l=void 0;else switch(l&&(u.push(-5),l=void 0),f<p&&(u.push(r.slice(f,p)),t+=p-f),m){case 0:{u.push(65533),t++;break}case 9:{for(s=Math.ceil(t/4)*4,u.push(-2);t++<s;)u.push(-1);break}case 10:{u.push(-4),t=1;break}default:l=!0,t=1}f=p+1}return o&&(l&&u.push(-5),e&&u.push(e),u.push(null)),u}}var qE=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Jb(t){return t.replace(qE,jE)}function jE(t,e,n){if(e)return e;if(n.charCodeAt(0)===35){let i=n.charCodeAt(1),r=i===120||i===88;return fu(n.slice(r?2:1),r?16:10)}return Vi(n)||t}var Pb={}.hasOwnProperty;function lm(t,e,n){return typeof e!="string"&&(n=e,e=void 0),GE(n)(em(tm(n).document().write(nm()(t,e,!0))))}function GE(t){let e={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(mp),autolinkProtocol:q,autolinkEmail:q,atxHeading:r(cp),blockQuote:r(se),characterEscape:q,characterReference:q,codeFenced:r(qe),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:r(qe,a),codeText:r(je,a),codeTextData:q,data:q,codeFlowValue:q,definition:r(Gu),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:r(mx),hardBreakEscape:r(sp),hardBreakTrailing:r(sp),htmlFlow:r(fp,a),htmlFlowData:q,htmlText:r(fp,a),htmlTextData:q,image:r(px),label:a,link:r(mp),listItem:r(hx),listItemValue:p,listOrdered:r(pp,f),listUnordered:r(pp),paragraph:r(dx),reference:b,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:r(cp),strong:r(gx),thematicBreak:r(bx)},exit:{atxHeading:u(),atxHeadingSequence:z,autolink:u(),autolinkEmail:Ze,autolinkProtocol:bt,blockQuote:u(),characterEscapeValue:k,characterReferenceMarkerHexadecimal:Lt,characterReferenceMarkerNumeric:Lt,characterReferenceValue:Dn,characterReference:gt,codeFenced:u(E),codeFencedFence:v,codeFencedFenceInfo:m,codeFencedFenceMeta:y,codeFlowValue:k,codeIndented:u(h),codeText:u(G),codeTextData:k,data:k,definition:u(),definitionDestinationString:S,definitionLabelString:d,definitionTitleString:g,emphasis:u(),hardBreakEscape:u(L),hardBreakTrailing:u(L),htmlFlow:u(_),htmlFlowData:k,htmlText:u(j),htmlTextData:k,image:u(lt),label:Kt,labelText:U,lineEnding:it,link:u(X),listItem:u(),listOrdered:u(),listUnordered:u(),paragraph:u(),referenceString:rt,resourceDestinationString:x,resourceTitleString:Dt,resource:ce,setextHeading:u(T),setextHeadingLineSequence:D,setextHeadingText:w,strong:u(),thematicBreak:u()}};$b(e,(t||{}).mdastExtensions||[]);let n={};return l;function l(A){let R={type:"root",children:[]},I={stack:[R],tokenStack:[],config:e,enter:o,exit:c,buffer:a,resume:s,data:n},et=[],pt=-1;for(;++pt<A.length;)if(A[pt][1].type==="listOrdered"||A[pt][1].type==="listUnordered")if(A[pt][0]==="enter")et.push(pt);else{let Ge=et.pop();pt=i(A,Ge,pt)}for(pt=-1;++pt<A.length;){let Ge=e[A[pt][0]];Pb.call(Ge,A[pt][1].type)&&Ge[A[pt][1].type].call(Object.assign({sliceSerialize:A[pt][2].sliceSerialize},I),A[pt][1])}if(I.tokenStack.length>0){let Ge=I.tokenStack[I.tokenStack.length-1];(Ge[1]||Wb).call(I,void 0,Ge[0])}for(R.position={start:ul(A.length>0?A[0][1].start:{line:1,column:1,offset:0}),end:ul(A.length>0?A[A.length-2][1].end:{line:1,column:1,offset:0})},pt=-1;++pt<e.transforms.length;)R=e.transforms[pt](R)||R;return R}function i(A,R,I){let et=R-1,pt=-1,Ge=!1,ml,on,er,nr;for(;++et<=I;){let ye=A[et];switch(ye[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{ye[0]==="enter"?pt++:pt--,nr=void 0;break}case"lineEndingBlank":{ye[0]==="enter"&&(ml&&!nr&&!pt&&!er&&(er=et),nr=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:nr=void 0}if(!pt&&ye[0]==="enter"&&ye[1].type==="listItemPrefix"||pt===-1&&ye[0]==="exit"&&(ye[1].type==="listUnordered"||ye[1].type==="listOrdered")){if(ml){let Zl=et;for(on=void 0;Zl--;){let un=A[Zl];if(un[1].type==="lineEnding"||un[1].type==="lineEndingBlank"){if(un[0]==="exit")continue;on&&(A[on][1].type="lineEndingBlank",Ge=!0),un[1].type="lineEnding",on=Zl}else if(!(un[1].type==="linePrefix"||un[1].type==="blockQuotePrefix"||un[1].type==="blockQuotePrefixWhitespace"||un[1].type==="blockQuoteMarker"||un[1].type==="listItemIndent"))break}er&&(!on||er<on)&&(ml._spread=!0),ml.end=Object.assign({},on?A[on][1].start:ye[1].end),A.splice(on||et,0,["exit",ml,ye[2]]),et++,I++}if(ye[1].type==="listItemPrefix"){let Zl={type:"listItem",_spread:!1,start:Object.assign({},ye[1].start),end:void 0};ml=Zl,A.splice(et,0,["enter",Zl,ye[2]]),et++,I++,er=void 0,nr=!0}}}return A[R][1]._spread=Ge,I}function r(A,R){return I;function I(et){o.call(this,A(et),et),R&&R.call(this,et)}}function a(){this.stack.push({type:"fragment",children:[]})}function o(A,R,I){this.stack[this.stack.length-1].children.push(A),this.stack.push(A),this.tokenStack.push([R,I||void 0]),A.position={start:ul(R.start),end:void 0}}function u(A){return R;function R(I){A&&A.call(this,I),c.call(this,I)}}function c(A,R){let I=this.stack.pop(),et=this.tokenStack.pop();if(et)et[0].type!==A.type&&(R?R.call(this,A,et[0]):(et[1]||Wb).call(this,A,et[0]));else throw new Error("Cannot close `"+A.type+"` ("+rl({start:A.start,end:A.end})+"): it\u2019s not open");I.position.end=ul(A.end)}function s(){return Ll(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(A){if(this.data.expectingFirstListItemValue){let R=this.stack[this.stack.length-2];R.start=Number.parseInt(this.sliceSerialize(A),10),this.data.expectingFirstListItemValue=void 0}}function m(){let A=this.resume(),R=this.stack[this.stack.length-1];R.lang=A}function y(){let A=this.resume(),R=this.stack[this.stack.length-1];R.meta=A}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function E(){let A=this.resume(),R=this.stack[this.stack.length-1];R.value=A.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function h(){let A=this.resume(),R=this.stack[this.stack.length-1];R.value=A.replace(/(\r?\n|\r)$/g,"")}function d(A){let R=this.resume(),I=this.stack[this.stack.length-1];I.label=R,I.identifier=ne(this.sliceSerialize(A)).toLowerCase()}function g(){let A=this.resume(),R=this.stack[this.stack.length-1];R.title=A}function S(){let A=this.resume(),R=this.stack[this.stack.length-1];R.url=A}function z(A){let R=this.stack[this.stack.length-1];if(!R.depth){let I=this.sliceSerialize(A).length;R.depth=I}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function D(A){let R=this.stack[this.stack.length-1];R.depth=this.sliceSerialize(A).codePointAt(0)===61?1:2}function T(){this.data.setextHeadingSlurpLineEnding=void 0}function q(A){let I=this.stack[this.stack.length-1].children,et=I[I.length-1];(!et||et.type!=="text")&&(et=yx(),et.position={start:ul(A.start),end:void 0},I.push(et)),this.stack.push(et)}function k(A){let R=this.stack.pop();R.value+=this.sliceSerialize(A),R.position.end=ul(A.end)}function it(A){let R=this.stack[this.stack.length-1];if(this.data.atHardBreak){let I=R.children[R.children.length-1];I.position.end=ul(A.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&e.canContainEols.includes(R.type)&&(q.call(this,A),k.call(this,A))}function L(){this.data.atHardBreak=!0}function _(){let A=this.resume(),R=this.stack[this.stack.length-1];R.value=A}function j(){let A=this.resume(),R=this.stack[this.stack.length-1];R.value=A}function G(){let A=this.resume(),R=this.stack[this.stack.length-1];R.value=A}function X(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=R,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function lt(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=R,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function U(A){let R=this.sliceSerialize(A),I=this.stack[this.stack.length-2];I.label=Jb(R),I.identifier=ne(R).toLowerCase()}function Kt(){let A=this.stack[this.stack.length-1],R=this.resume(),I=this.stack[this.stack.length-1];if(this.data.inReference=!0,I.type==="link"){let et=A.children;I.children=et}else I.alt=R}function x(){let A=this.resume(),R=this.stack[this.stack.length-1];R.url=A}function Dt(){let A=this.resume(),R=this.stack[this.stack.length-1];R.title=A}function ce(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function rt(A){let R=this.resume(),I=this.stack[this.stack.length-1];I.label=R,I.identifier=ne(this.sliceSerialize(A)).toLowerCase(),this.data.referenceType="full"}function Lt(A){this.data.characterReferenceType=A.type}function Dn(A){let R=this.sliceSerialize(A),I=this.data.characterReferenceType,et;I?(et=fu(R,I==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):et=Vi(R);let pt=this.stack[this.stack.length-1];pt.value+=et}function gt(A){let R=this.stack.pop();R.position.end=ul(A.end)}function bt(A){k.call(this,A);let R=this.stack[this.stack.length-1];R.url=this.sliceSerialize(A)}function Ze(A){k.call(this,A);let R=this.stack[this.stack.length-1];R.url="mailto:"+this.sliceSerialize(A)}function se(){return{type:"blockquote",children:[]}}function qe(){return{type:"code",lang:null,meta:null,value:""}}function je(){return{type:"inlineCode",value:""}}function Gu(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function mx(){return{type:"emphasis",children:[]}}function cp(){return{type:"heading",depth:0,children:[]}}function sp(){return{type:"break"}}function fp(){return{type:"html",value:""}}function px(){return{type:"image",title:null,url:"",alt:null}}function mp(){return{type:"link",title:null,url:"",children:[]}}function pp(A){return{type:"list",ordered:A.type==="listOrdered",start:null,spread:A._spread,children:[]}}function hx(A){return{type:"listItem",spread:A._spread,checked:null,children:[]}}function dx(){return{type:"paragraph",children:[]}}function gx(){return{type:"strong",children:[]}}function yx(){return{type:"text",value:""}}function bx(){return{type:"thematicBreak"}}}function ul(t){return{line:t.line,column:t.column,offset:t.offset}}function $b(t,e){let n=-1;for(;++n<e.length;){let l=e[n];Array.isArray(l)?$b(t,l):YE(t,l)}}function YE(t,e){let n;for(n in e)if(Pb.call(e,n))switch(n){case"canContainEols":{let l=e[n];l&&t[n].push(...l);break}case"transforms":{let l=e[n];l&&t[n].push(...l);break}case"enter":case"exit":{let l=e[n];l&&Object.assign(t[n],l);break}}}function Wb(t,e){throw t?new Error("Cannot close `"+t.type+"` ("+rl({start:t.start,end:t.end})+"): a different token (`"+e.type+"`, "+rl({start:e.start,end:e.end})+") is open"):new Error("Cannot close document, a token (`"+e.type+"`, "+rl({start:e.start,end:e.end})+") is still open")}function ku(t){let e=this;e.parser=n;function n(l){return lm(l,Et(C(C({},e.data("settings")),t),{extensions:e.data("micromarkExtensions")||[],mdastExtensions:e.data("fromMarkdownExtensions")||[]}))}}function t1(t,e){let n={type:"element",tagName:"blockquote",properties:{},children:t.wrap(t.all(e),!0)};return t.patch(e,n),t.applyData(e,n)}function e1(t,e){let n={type:"element",tagName:"br",properties:{},children:[]};return t.patch(e,n),[t.applyData(e,n),{type:"text",value:`
`}]}function n1(t,e){let n=e.value?e.value+`
`:"",l={};e.lang&&(l.className=["language-"+e.lang]);let i={type:"element",tagName:"code",properties:l,children:[{type:"text",value:n}]};return e.meta&&(i.data={meta:e.meta}),t.patch(e,i),i=t.applyData(e,i),i={type:"element",tagName:"pre",properties:{},children:[i]},t.patch(e,i),i}function l1(t,e){let n={type:"element",tagName:"del",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function i1(t,e){let n={type:"element",tagName:"em",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function r1(t,e){let n=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",l=String(e.identifier).toUpperCase(),i=He(l.toLowerCase()),r=t.footnoteOrder.indexOf(l),a,o=t.footnoteCounts.get(l);o===void 0?(o=0,t.footnoteOrder.push(l),a=t.footnoteOrder.length):a=r+1,o+=1,t.footnoteCounts.set(l,o);let u={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};t.patch(e,u);let c={type:"element",tagName:"sup",properties:{},children:[u]};return t.patch(e,c),t.applyData(e,c)}function a1(t,e){let n={type:"element",tagName:"h"+e.depth,properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function o1(t,e){if(t.options.allowDangerousHtml){let n={type:"raw",value:e.value};return t.patch(e,n),t.applyData(e,n)}}function wu(t,e){let n=e.referenceType,l="]";if(n==="collapsed"?l+="[]":n==="full"&&(l+="["+(e.label||e.identifier)+"]"),e.type==="imageReference")return[{type:"text",value:"!["+e.alt+l}];let i=t.all(e),r=i[0];r&&r.type==="text"?r.value="["+r.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&a.type==="text"?a.value+=l:i.push({type:"text",value:l}),i}function u1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return wu(t,e);let i={src:He(l.url||""),alt:e.alt};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"img",properties:i,children:[]};return t.patch(e,r),t.applyData(e,r)}function c1(t,e){let n={src:He(e.url)};e.alt!==null&&e.alt!==void 0&&(n.alt=e.alt),e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"img",properties:n,children:[]};return t.patch(e,l),t.applyData(e,l)}function s1(t,e){let n={type:"text",value:e.value.replace(/\r?\n|\r/g," ")};t.patch(e,n);let l={type:"element",tagName:"code",properties:{},children:[n]};return t.patch(e,l),t.applyData(e,l)}function f1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return wu(t,e);let i={href:He(l.url||"")};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"a",properties:i,children:t.all(e)};return t.patch(e,r),t.applyData(e,r)}function m1(t,e){let n={href:He(e.url)};e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"a",properties:n,children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function p1(t,e,n){let l=t.all(e),i=n?VE(n):h1(e),r={},a=[];if(typeof e.checked=="boolean"){let s=l[0],f;s&&s.type==="element"&&s.tagName==="p"?f=s:(f={type:"element",tagName:"p",properties:{},children:[]},l.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:e.checked,disabled:!0},children:[]}),r.className=["task-list-item"]}let o=-1;for(;++o<l.length;){let s=l[o];(i||o!==0||s.type!=="element"||s.tagName!=="p")&&a.push({type:"text",value:`
`}),s.type==="element"&&s.tagName==="p"&&!i?a.push(...s.children):a.push(s)}let u=l[l.length-1];u&&(i||u.type!=="element"||u.tagName!=="p")&&a.push({type:"text",value:`
`});let c={type:"element",tagName:"li",properties:r,children:a};return t.patch(e,c),t.applyData(e,c)}function VE(t){let e=!1;if(t.type==="list"){e=t.spread||!1;let n=t.children,l=-1;for(;!e&&++l<n.length;)e=h1(n[l])}return e}function h1(t){let e=t.spread;return e==null?t.children.length>1:e}function d1(t,e){let n={},l=t.all(e),i=-1;for(typeof e.start=="number"&&e.start!==1&&(n.start=e.start);++i<l.length;){let a=l[i];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let r={type:"element",tagName:e.ordered?"ol":"ul",properties:n,children:t.wrap(l,!0)};return t.patch(e,r),t.applyData(e,r)}function g1(t,e){let n={type:"element",tagName:"p",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function y1(t,e){let n={type:"root",children:t.wrap(t.all(e))};return t.patch(e,n),t.applyData(e,n)}function b1(t,e){let n={type:"element",tagName:"strong",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function x1(t,e){let n=t.all(e),l=n.shift(),i=[];if(l){let a={type:"element",tagName:"thead",properties:{},children:t.wrap([l],!0)};t.patch(e.children[0],a),i.push(a)}if(n.length>0){let a={type:"element",tagName:"tbody",properties:{},children:t.wrap(n,!0)},o=Gi(e.children[1]),u=uu(e.children[e.children.length-1]);o&&u&&(a.position={start:o,end:u}),i.push(a)}let r={type:"element",tagName:"table",properties:{},children:t.wrap(i,!0)};return t.patch(e,r),t.applyData(e,r)}function v1(t,e,n){let l=n?n.children:void 0,r=(l?l.indexOf(e):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,o=a?a.length:e.children.length,u=-1,c=[];for(;++u<o;){let f=e.children[u],p={},m=a?a[u]:void 0;m&&(p.align=m);let y={type:"element",tagName:r,properties:p,children:[]};f&&(y.children=t.all(f),t.patch(f,y),y=t.applyData(f,y)),c.push(y)}let s={type:"element",tagName:"tr",properties:{},children:t.wrap(c,!0)};return t.patch(e,s),t.applyData(e,s)}function S1(t,e){let n={type:"element",tagName:"td",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function w1(t){let e=String(t),n=/\r?\n|\r/g,l=n.exec(e),i=0,r=[];for(;l;)r.push(k1(e.slice(i,l.index),i>0,!0),l[0]),i=l.index+l[0].length,l=n.exec(e);return r.push(k1(e.slice(i),i>0,!1)),r.join("")}function k1(t,e,n){let l=0,i=t.length;if(e){let r=t.codePointAt(l);for(;r===9||r===32;)l++,r=t.codePointAt(l)}if(n){let r=t.codePointAt(i-1);for(;r===9||r===32;)i--,r=t.codePointAt(i-1)}return i>l?t.slice(l,i):""}function E1(t,e){let n={type:"text",value:w1(String(e.value))};return t.patch(e,n),t.applyData(e,n)}function T1(t,e){let n={type:"element",tagName:"hr",properties:{},children:[]};return t.patch(e,n),t.applyData(e,n)}var A1={blockquote:t1,break:e1,code:n1,delete:l1,emphasis:i1,footnoteReference:r1,heading:a1,html:o1,imageReference:u1,image:c1,inlineCode:s1,linkReference:f1,link:m1,listItem:p1,list:d1,paragraph:g1,root:y1,strong:b1,table:x1,tableCell:S1,tableRow:v1,text:E1,thematicBreak:T1,toml:Eu,yaml:Eu,definition:Eu,footnoteDefinition:Eu};function Eu(){}var z1=typeof self=="object"?self:globalThis,IE=(t,e)=>{let n=(i,r)=>(t.set(r,i),i),l=i=>{if(t.has(i))return t.get(i);let[r,a]=e[i];switch(r){case 0:case-1:return n(a,i);case 1:{let o=n([],i);for(let u of a)o.push(l(u));return o}case 2:{let o=n({},i);for(let[u,c]of a)o[l(u)]=l(c);return o}case 3:return n(new Date(a),i);case 4:{let{source:o,flags:u}=a;return n(new RegExp(o,u),i)}case 5:{let o=n(new Map,i);for(let[u,c]of a)o.set(l(u),l(c));return o}case 6:{let o=n(new Set,i);for(let u of a)o.add(l(u));return o}case 7:{let{name:o,message:u}=a;return n(new z1[o](u),i)}case 8:return n(BigInt(a),i);case"BigInt":return n(Object(BigInt(a)),i);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:o}=new Uint8Array(a);return n(new DataView(o),a)}}return n(new z1[r](a),i)};return l},am=t=>IE(new Map,t)(0);var Xi="",{toString:FE}={},{keys:KE}=Object,va=t=>{let e=typeof t;if(e!=="object"||!t)return[0,e];let n=FE.call(t).slice(8,-1);switch(n){case"Array":return[1,Xi];case"Object":return[2,Xi];case"Date":return[3,Xi];case"RegExp":return[4,Xi];case"Map":return[5,Xi];case"Set":return[6,Xi];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},Au=([t,e])=>t===0&&(e==="function"||e==="symbol"),JE=(t,e,n,l)=>{let i=(a,o)=>{let u=l.push(a)-1;return n.set(o,u),u},r=a=>{if(n.has(a))return n.get(a);let[o,u]=va(a);switch(o){case 0:{let s=a;switch(u){case"bigint":o=8,s=a.toString();break;case"function":case"symbol":if(t)throw new TypeError("unable to serialize "+u);s=null;break;case"undefined":return i([-1],a)}return i([o,s],a)}case 1:{if(u){let p=a;return u==="DataView"?p=new Uint8Array(a.buffer):u==="ArrayBuffer"&&(p=new Uint8Array(a)),i([u,[...p]],a)}let s=[],f=i([o,s],a);for(let p of a)s.push(r(p));return f}case 2:{if(u)switch(u){case"BigInt":return i([u,a.toString()],a);case"Boolean":case"Number":case"String":return i([u,a.valueOf()],a)}if(e&&"toJSON"in a)return r(a.toJSON());let s=[],f=i([o,s],a);for(let p of KE(a))(t||!Au(va(a[p])))&&s.push([r(p),r(a[p])]);return f}case 3:return i([o,a.toISOString()],a);case 4:{let{source:s,flags:f}=a;return i([o,{source:s,flags:f}],a)}case 5:{let s=[],f=i([o,s],a);for(let[p,m]of a)(t||!(Au(va(p))||Au(va(m))))&&s.push([r(p),r(m)]);return f}case 6:{let s=[],f=i([o,s],a);for(let p of a)(t||!Au(va(p)))&&s.push(r(p));return f}}let{message:c}=a;return i([o,{name:u,message:c}],a)};return r},om=(t,{json:e,lossy:n}={})=>{let l=[];return JE(!(e||n),!!e,new Map,l)(t),l};var Qi=typeof structuredClone=="function"?(t,e)=>e&&("json"in e||"lossy"in e)?am(om(t,e)):structuredClone(t):(t,e)=>am(om(t,e));function WE(t,e){let n=[{type:"text",value:"\u21A9"}];return e>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(e)}]}),n}function PE(t,e){return"Back to reference "+(t+1)+(e>1?"-"+e:"")}function R1(t){let e=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",n=t.options.footnoteBackContent||WE,l=t.options.footnoteBackLabel||PE,i=t.options.footnoteLabel||"Footnotes",r=t.options.footnoteLabelTagName||"h2",a=t.options.footnoteLabelProperties||{className:["sr-only"]},o=[],u=-1;for(;++u<t.footnoteOrder.length;){let c=t.footnoteById.get(t.footnoteOrder[u]);if(!c)continue;let s=t.all(c),f=String(c.identifier).toUpperCase(),p=He(f.toLowerCase()),m=0,y=[],v=t.footnoteCounts.get(f);for(;v!==void 0&&++m<=v;){y.length>0&&y.push({type:"text",value:" "});let d=typeof n=="string"?n:n(u,m);typeof d=="string"&&(d={type:"text",value:d}),y.push({type:"element",tagName:"a",properties:{href:"#"+e+"fnref-"+p+(m>1?"-"+m:""),dataFootnoteBackref:"",ariaLabel:typeof l=="string"?l:l(u,m),className:["data-footnote-backref"]},children:Array.isArray(d)?d:[d]})}let E=s[s.length-1];if(E&&E.type==="element"&&E.tagName==="p"){let d=E.children[E.children.length-1];d&&d.type==="text"?d.value+=" ":E.children.push({type:"text",value:" "}),E.children.push(...y)}else s.push(...y);let h={type:"element",tagName:"li",properties:{id:e+"fn-"+p},children:t.wrap(s,!0)};t.patch(c,h),o.push(h)}if(o.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:r,properties:Et(C({},Qi(a)),{id:"footnote-label"}),children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:t.wrap(o,!0)},{type:"text",value:`
`}]}}var cl=function(t){if(t==null)return n2;if(typeof t=="function")return zu(t);if(typeof t=="object")return Array.isArray(t)?$E(t):t2(t);if(typeof t=="string")return e2(t);throw new Error("Expected function, string, or object as test")};function $E(t){let e=[],n=-1;for(;++n<t.length;)e[n]=cl(t[n]);return zu(l);function l(...i){let r=-1;for(;++r<e.length;)if(e[r].apply(this,i))return!0;return!1}}function t2(t){let e=t;return zu(n);function n(l){let i=l,r;for(r in t)if(i[r]!==e[r])return!1;return!0}}function e2(t){return zu(e);function e(n){return n&&n.type===t}}function zu(t){return e;function e(n,l,i){return!!(l2(n)&&t.call(this,n,typeof l=="number"?l:void 0,i||void 0))}}function n2(){return!0}function l2(t){return t!==null&&typeof t=="object"&&"type"in t}var _1=[],Cu=!0,Gl=!1,Mu="skip";function Sa(t,e,n,l){let i;typeof e=="function"&&typeof n!="function"?(l=n,n=e):i=e;let r=cl(i),a=l?-1:1;o(t,void 0,[])();function o(u,c,s){let f=u&&typeof u=="object"?u:{};if(typeof f.type=="string"){let m=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(u.type+(m?"<"+m+">":""))+")"})}return p;function p(){let m=_1,y,v,E;if((!e||r(u,c,s[s.length-1]||void 0))&&(m=i2(n(u,s)),m[0]===Gl))return m;if("children"in u&&u.children){let h=u;if(h.children&&m[0]!==Mu)for(v=(l?h.children.length:-1)+a,E=s.concat(h);v>-1&&v<h.children.length;){let d=h.children[v];if(y=o(d,v,E)(),y[0]===Gl)return y;v=typeof y[1]=="number"?y[1]:v+a}}return m}}}function i2(t){return Array.isArray(t)?t:typeof t=="number"?[Cu,t]:t==null?_1:[t]}function Yl(t,e,n,l){let i,r,a;typeof e=="function"&&typeof n!="function"?(r=void 0,a=e,i=n):(r=e,a=n,i=l),Sa(t,r,o,i);function o(u,c){let s=c[c.length-1],f=s?s.children.indexOf(u):void 0;return a(u,f,s)}}var um={}.hasOwnProperty,r2={};function L1(t,e){let n=e||r2,l=new Map,i=new Map,r=new Map,a=C(C({},A1),n.handlers),o={all:c,applyData:o2,definitionById:l,footnoteById:i,footnoteCounts:r,footnoteOrder:[],handlers:a,one:u,options:n,patch:a2,wrap:c2};return Yl(t,function(s){if(s.type==="definition"||s.type==="footnoteDefinition"){let f=s.type==="definition"?l:i,p=String(s.identifier).toUpperCase();f.has(p)||f.set(p,s)}}),o;function u(s,f){let p=s.type,m=o.handlers[p];if(um.call(o.handlers,p)&&m)return m(o,s,f);if(o.options.passThrough&&o.options.passThrough.includes(p)){if("children"in s){let v=s,{children:E}=v,h=Ca(v,["children"]),d=Qi(h);return d.children=o.all(s),d}return Qi(s)}return(o.options.unknownHandler||u2)(o,s,f)}function c(s){let f=[];if("children"in s){let p=s.children,m=-1;for(;++m<p.length;){let y=o.one(p[m],s);if(y){if(m&&p[m-1].type==="break"&&(!Array.isArray(y)&&y.type==="text"&&(y.value=N1(y.value)),!Array.isArray(y)&&y.type==="element")){let v=y.children[0];v&&v.type==="text"&&(v.value=N1(v.value))}Array.isArray(y)?f.push(...y):f.push(y)}}}return f}}function a2(t,e){t.position&&(e.position=Uf(t))}function o2(t,e){let n=e;if(t&&t.data){let l=t.data.hName,i=t.data.hChildren,r=t.data.hProperties;if(typeof l=="string")if(n.type==="element")n.tagName=l;else{let a="children"in n?n.children:[n];n={type:"element",tagName:l,properties:{},children:a}}n.type==="element"&&r&&Object.assign(n.properties,Qi(r)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function u2(t,e){let n=e.data||{},l="value"in e&&!(um.call(n,"hProperties")||um.call(n,"hChildren"))?{type:"text",value:e.value}:{type:"element",tagName:"div",properties:{},children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function c2(t,e){let n=[],l=-1;for(e&&n.push({type:"text",value:`
`});++l<t.length;)l&&n.push({type:"text",value:`
`}),n.push(t[l]);return e&&t.length>0&&n.push({type:"text",value:`
`}),n}function N1(t){let e=0,n=t.charCodeAt(e);for(;n===9||n===32;)e++,n=t.charCodeAt(e);return t.slice(e)}function Du(t,e){let n=L1(t,e),l=n.one(t,void 0),i=R1(n),r=Array.isArray(l)?{type:"root",children:l}:l||{type:"root",children:[]};return i&&("children"in r,r.children.push({type:"text",value:`
`},i)),r}function Ou(t,e){return t&&"run"in t?function(n,l){return Il(this,null,function*(){let i=Du(n,C({file:l},e));yield t.run(i,l)})}:function(n,l){return Du(n,C({file:l},t||e))}}function cm(t){if(t)throw t}var Nu=Ie(X1(),1);function ka(t){if(typeof t!="object"||t===null)return!1;let e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)}function sm(){let t=[],e={run:n,use:l};return e;function n(...i){let r=-1,a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);o(null,...i);function o(u,...c){let s=t[++r],f=-1;if(u){a(u);return}for(;++f<i.length;)(c[f]===null||c[f]===void 0)&&(c[f]=i[f]);i=c,s?Q1(s,o)(...c):a(null,...c)}}function l(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return t.push(i),e}}function Q1(t,e){let n;return l;function l(...a){let o=t.length>a.length,u;o&&a.push(i);try{u=t.apply(this,a)}catch(c){let s=c;if(o&&n)throw s;return i(s)}o||(u&&u.then&&typeof u.then=="function"?u.then(r,i):u instanceof Error?i(u):r(u))}function i(a,...o){n||(n=!0,e(a,...o))}function r(a){i(null,a)}}var Qe={basename:s2,dirname:f2,extname:m2,join:p2,sep:"/"};function s2(t,e){if(e!==void 0&&typeof e!="string")throw new TypeError('"ext" argument must be a string');wa(t);let n=0,l=-1,i=t.length,r;if(e===void 0||e.length===0||e.length>t.length){for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else l<0&&(r=!0,l=i+1);return l<0?"":t.slice(n,l)}if(e===t)return"";let a=-1,o=e.length-1;for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else a<0&&(r=!0,a=i+1),o>-1&&(t.codePointAt(i)===e.codePointAt(o--)?o<0&&(l=i):(o=-1,l=a));return n===l?l=a:l<0&&(l=t.length),t.slice(n,l)}function f2(t){if(wa(t),t.length===0)return".";let e=-1,n=t.length,l;for(;--n;)if(t.codePointAt(n)===47){if(l){e=n;break}}else l||(l=!0);return e<0?t.codePointAt(0)===47?"/":".":e===1&&t.codePointAt(0)===47?"//":t.slice(0,e)}function m2(t){wa(t);let e=t.length,n=-1,l=0,i=-1,r=0,a;for(;e--;){let o=t.codePointAt(e);if(o===47){if(a){l=e+1;break}continue}n<0&&(a=!0,n=e+1),o===46?i<0?i=e:r!==1&&(r=1):i>-1&&(r=-1)}return i<0||n<0||r===0||r===1&&i===n-1&&i===l+1?"":t.slice(i,n)}function p2(...t){let e=-1,n;for(;++e<t.length;)wa(t[e]),t[e]&&(n=n===void 0?t[e]:n+"/"+t[e]);return n===void 0?".":h2(n)}function h2(t){wa(t);let e=t.codePointAt(0)===47,n=d2(t,!e);return n.length===0&&!e&&(n="."),n.length>0&&t.codePointAt(t.length-1)===47&&(n+="/"),e?"/"+n:n}function d2(t,e){let n="",l=0,i=-1,r=0,a=-1,o,u;for(;++a<=t.length;){if(a<t.length)o=t.codePointAt(a);else{if(o===47)break;o=47}if(o===47){if(!(i===a-1||r===1))if(i!==a-1&&r===2){if(n.length<2||l!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(u=n.lastIndexOf("/"),u!==n.length-1){u<0?(n="",l=0):(n=n.slice(0,u),l=n.length-1-n.lastIndexOf("/")),i=a,r=0;continue}}else if(n.length>0){n="",l=0,i=a,r=0;continue}}e&&(n=n.length>0?n+"/..":"..",l=2)}else n.length>0?n+="/"+t.slice(i+1,a):n=t.slice(i+1,a),l=a-i-1;i=a,r=0}else o===46&&r>-1?r++:r=-1}return n}function wa(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}var Z1={cwd:g2};function g2(){return"/"}function Zi(t){return!!(t!==null&&typeof t=="object"&&"href"in t&&t.href&&"protocol"in t&&t.protocol&&t.auth===void 0)}function I1(t){if(typeof t=="string")t=new URL(t);else if(!Zi(t)){let e=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+t+"`");throw e.code="ERR_INVALID_ARG_TYPE",e}if(t.protocol!=="file:"){let e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return y2(t)}function y2(t){if(t.hostname!==""){let l=new TypeError('File URL host must be "localhost" or empty on darwin');throw l.code="ERR_INVALID_FILE_URL_HOST",l}let e=t.pathname,n=-1;for(;++n<e.length;)if(e.codePointAt(n)===37&&e.codePointAt(n+1)===50){let l=e.codePointAt(n+2);if(l===70||l===102){let i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(e)}var fm=["history","path","basename","stem","extname","dirname"],Vl=class{constructor(e){let n;e?Zi(e)?n={path:e}:typeof e=="string"||b2(e)?n={value:e}:n=e:n={},this.cwd="cwd"in n?"":Z1.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let l=-1;for(;++l<fm.length;){let r=fm[l];r in n&&n[r]!==void 0&&n[r]!==null&&(this[r]=r==="history"?[...n[r]]:n[r])}let i;for(i in n)fm.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Qe.basename(this.path):void 0}set basename(e){pm(e,"basename"),mm(e,"basename"),this.path=Qe.join(this.dirname||"",e)}get dirname(){return typeof this.path=="string"?Qe.dirname(this.path):void 0}set dirname(e){F1(this.basename,"dirname"),this.path=Qe.join(e||"",this.basename)}get extname(){return typeof this.path=="string"?Qe.extname(this.path):void 0}set extname(e){if(mm(e,"extname"),F1(this.dirname,"extname"),e){if(e.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Qe.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Zi(e)&&(e=I1(e)),pm(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return typeof this.path=="string"?Qe.basename(this.path,this.extname):void 0}set stem(e){pm(e,"stem"),mm(e,"stem"),this.path=Qe.join(this.dirname||"",e+(this.extname||""))}fail(e,n,l){let i=this.message(e,n,l);throw i.fatal=!0,i}info(e,n,l){let i=this.message(e,n,l);return i.fatal=void 0,i}message(e,n,l){let i=new _t(e,n,l);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(e){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(e||void 0).decode(this.value)}};function mm(t,e){if(t&&t.includes(Qe.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+Qe.sep+"`")}function pm(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function F1(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}function b2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var K1=function(t){let l=this.constructor.prototype,i=l[t],r=function(){return i.apply(r,arguments)};return Object.setPrototypeOf(r,l),r};var x2={}.hasOwnProperty,ym=class t extends K1{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=sm()}copy(){let e=new t,n=-1;for(;++n<this.attachers.length;){let l=this.attachers[n];e.use(...l)}return e.data((0,Nu.default)(!0,{},this.namespace)),e}data(e,n){return typeof e=="string"?arguments.length===2?(gm("data",this.frozen),this.namespace[e]=n,this):x2.call(this.namespace,e)&&this.namespace[e]||void 0:e?(gm("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;let e=this;for(;++this.freezeIndex<this.attachers.length;){let[n,...l]=this.attachers[this.freezeIndex];if(l[0]===!1)continue;l[0]===!0&&(l[0]=void 0);let i=n.call(e,...l);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let n=_u(e),l=this.parser||this.Parser;return hm("parse",l),l(String(n),n)}process(e,n){let l=this;return this.freeze(),hm("process",this.parser||this.Parser),dm("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(r,a){let o=_u(e),u=l.parse(o);l.run(u,o,function(s,f,p){if(s||!f||!p)return c(s);let m=f,y=l.stringify(m,p);S2(y)?p.value=y:p.result=y,c(s,p)});function c(s,f){s||!f?a(s):r?r(f):n(void 0,f)}}}processSync(e){let n=!1,l;return this.freeze(),hm("processSync",this.parser||this.Parser),dm("processSync",this.compiler||this.Compiler),this.process(e,i),W1("processSync","process",n),l;function i(r,a){n=!0,cm(r),l=a}}run(e,n,l){J1(e),this.freeze();let i=this.transformers;return!l&&typeof n=="function"&&(l=n,n=void 0),l?r(void 0,l):new Promise(r);function r(a,o){let u=_u(n);i.run(e,u,c);function c(s,f,p){let m=f||e;s?o(s):a?a(m):l(void 0,m,p)}}}runSync(e,n){let l=!1,i;return this.run(e,n,r),W1("runSync","run",l),i;function r(a,o){cm(a),i=o,l=!0}}stringify(e,n){this.freeze();let l=_u(n),i=this.compiler||this.Compiler;return dm("stringify",i),J1(e),i(e,l)}use(e,...n){let l=this.attachers,i=this.namespace;if(gm("use",this.frozen),e!=null)if(typeof e=="function")u(e,n);else if(typeof e=="object")Array.isArray(e)?o(e):a(e);else throw new TypeError("Expected usable value, not `"+e+"`");return this;function r(c){if(typeof c=="function")u(c,[]);else if(typeof c=="object")if(Array.isArray(c)){let[s,...f]=c;u(s,f)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(c.plugins),c.settings&&(i.settings=(0,Nu.default)(!0,i.settings,c.settings))}function o(c){let s=-1;if(c!=null)if(Array.isArray(c))for(;++s<c.length;){let f=c[s];r(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function u(c,s){let f=-1,p=-1;for(;++f<l.length;)if(l[f][0]===c){p=f;break}if(p===-1)l.push([c,...s]);else if(s.length>0){let[m,...y]=s,v=l[p][1];ka(v)&&ka(m)&&(m=(0,Nu.default)(!0,v,m)),l[p]=[c,m,...y]}}}},bm=new ym().freeze();function hm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `parser`")}function dm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `compiler`")}function gm(t,e){if(e)throw new Error("Cannot call `"+t+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function J1(t){if(!ka(t)||typeof t.type!="string")throw new TypeError("Expected node, got `"+t+"`")}function W1(t,e,n){if(!n)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}function _u(t){return v2(t)?t:new Vl(t)}function v2(t){return!!(t&&typeof t=="object"&&"message"in t&&"messages"in t)}function S2(t){return typeof t=="string"||k2(t)}function k2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var w2="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",P1=[],$1={allowDangerousHtml:!0},E2=/^(https?|ircs?|mailto|xmpp)$/i,T2=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function xm(t){let e=A2(t),n=z2(t);return C2(e.runSync(e.parse(n),n),t)}function A2(t){let e=t.rehypePlugins||P1,n=t.remarkPlugins||P1,l=t.remarkRehypeOptions?C(C({},t.remarkRehypeOptions),$1):$1;return bm().use(ku).use(n).use(Ou,l).use(e)}function z2(t){let e=t.children||"",n=new Vl;return typeof e=="string"?n.value=e:(""+e,void 0),n}function C2(t,e){let n=e.allowedElements,l=e.allowElement,i=e.components,r=e.disallowedElements,a=e.skipHtml,o=e.unwrapDisallowed,u=e.urlTransform||e0;for(let s of T2)Object.hasOwn(e,s.from)&&(""+s.from+(s.to?"use `"+s.to+"` instead":"remove it")+w2+s.id,void 0);return n&&r&&void 0,Yl(t,c),qf(t,{Fragment:Ii.Fragment,components:i,ignoreInvalidStyle:!0,jsx:Ii.jsx,jsxs:Ii.jsxs,passKeys:!0,passNode:!0});function c(s,f,p){if(s.type==="raw"&&p&&typeof f=="number")return a?p.children.splice(f,1):p.children[f]={type:"text",value:s.value},f;if(s.type==="element"){let m;for(m in ha)if(Object.hasOwn(ha,m)&&Object.hasOwn(s.properties,m)){let y=s.properties[m],v=ha[m];(v===null||v.includes(s.tagName))&&(s.properties[m]=u(String(y||""),m,s))}}if(s.type==="element"){let m=n?!n.includes(s.tagName):r?r.includes(s.tagName):!1;if(!m&&l&&typeof f=="number"&&(m=!l(s,f,p)),m&&p&&typeof f=="number")return o&&s.children?p.children.splice(f,1,...s.children):p.children.splice(f,1),f}}}function e0(t){let e=t.indexOf(":"),n=t.indexOf("?"),l=t.indexOf("#"),i=t.indexOf("/");return e===-1||i!==-1&&e>i||n!==-1&&e>n||l!==-1&&e>l||E2.test(t.slice(0,e))?t:""}function vm(t,e){let n=String(t);if(typeof e!="string")throw new TypeError("Expected character");let l=0,i=n.indexOf(e);for(;i!==-1;)l++,i=n.indexOf(e,i+e.length);return l}function Sm(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function km(t,e,n){let i=cl((n||{}).ignore||[]),r=M2(e),a=-1;for(;++a<r.length;)Sa(t,"text",o);function o(c,s){let f=-1,p;for(;++f<s.length;){let m=s[f],y=p?p.children:void 0;if(i(m,y?y.indexOf(m):void 0,p))return;p=m}if(p)return u(c,s)}function u(c,s){let f=s[s.length-1],p=r[a][0],m=r[a][1],y=0,E=f.children.indexOf(c),h=!1,d=[];p.lastIndex=0;let g=p.exec(c.value);for(;g;){let S=g.index,z={index:g.index,input:g.input,stack:[...s,c]},w=m(...g,z);if(typeof w=="string"&&(w=w.length>0?{type:"text",value:w}:void 0),w===!1?p.lastIndex=S+1:(y!==S&&d.push({type:"text",value:c.value.slice(y,S)}),Array.isArray(w)?d.push(...w):w&&d.push(w),y=S+g[0].length,h=!0),!p.global)break;g=p.exec(c.value)}return h?(y<c.value.length&&d.push({type:"text",value:c.value.slice(y)}),f.children.splice(E,1,...d)):d=[c],E+d.length}}function M2(t){let e=[];if(!Array.isArray(t))throw new TypeError("Expected find and replace tuple or list of tuples");let n=!t[0]||Array.isArray(t[0])?t:[t],l=-1;for(;++l<n.length;){let i=n[l];e.push([D2(i[0]),O2(i[1])])}return e}function D2(t){return typeof t=="string"?new RegExp(Sm(t),"g"):t}function O2(t){return typeof t=="function"?t:function(){return t}}var wm="phrasing",Em=["autolink","link","image","label"];function Am(){return{transforms:[B2],enter:{literalAutolink:R2,literalAutolinkEmail:Tm,literalAutolinkHttp:Tm,literalAutolinkWww:Tm},exit:{literalAutolink:U2,literalAutolinkEmail:L2,literalAutolinkHttp:_2,literalAutolinkWww:N2}}}function zm(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:wm,notInConstruct:Em},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:wm,notInConstruct:Em},{character:":",before:"[ps]",after:"\\/",inConstruct:wm,notInConstruct:Em}]}}function R2(t){this.enter({type:"link",title:null,url:"",children:[]},t)}function Tm(t){this.config.enter.autolinkProtocol.call(this,t)}function _2(t){this.config.exit.autolinkProtocol.call(this,t)}function N2(t){this.config.exit.data.call(this,t);let e=this.stack[this.stack.length-1];e.type,e.url="http://"+this.sliceSerialize(t)}function L2(t){this.config.exit.autolinkEmail.call(this,t)}function U2(t){this.exit(t)}function B2(t){km(t,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,H2],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),q2]],{ignore:["link","linkReference"]})}function H2(t,e,n,l,i){let r="";if(!n0(i)||(/^w/i.test(e)&&(n=e+n,e="",r="http://"),!j2(n)))return!1;let a=G2(n+l);if(!a[0])return!1;let o={type:"link",title:null,url:r+e+a[0],children:[{type:"text",value:e+a[0]}]};return a[1]?[o,{type:"text",value:a[1]}]:o}function q2(t,e,n,l){return!n0(l,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+e+"@"+n,children:[{type:"text",value:e+"@"+n}]}}function j2(t){let e=t.split(".");return!(e.length<2||e[e.length-1]&&(/_/.test(e[e.length-1])||!/[a-zA-Z\d]/.test(e[e.length-1]))||e[e.length-2]&&(/_/.test(e[e.length-2])||!/[a-zA-Z\d]/.test(e[e.length-2])))}function G2(t){let e=/[!"&'),.:;<>?\]}]+$/.exec(t);if(!e)return[t,void 0];t=t.slice(0,e.index);let n=e[0],l=n.indexOf(")"),i=vm(t,"("),r=vm(t,")");for(;l!==-1&&i>r;)t+=n.slice(0,l+1),n=n.slice(l+1),l=n.indexOf(")"),r++;return[t,n]}function n0(t,e){let n=t.input.charCodeAt(t.index-1);return(t.index===0||ln(n)||Bl(n))&&(!e||n!==47)}l0.peek=J2;function Y2(){this.buffer()}function V2(t){this.enter({type:"footnoteReference",identifier:"",label:""},t)}function X2(){this.buffer()}function Q2(t){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},t)}function Z2(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ne(this.sliceSerialize(t)).toLowerCase(),n.label=e}function I2(t){this.exit(t)}function F2(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ne(this.sliceSerialize(t)).toLowerCase(),n.label=e}function K2(t){this.exit(t)}function J2(){return"["}function l0(t,e,n,l){let i=n.createTracker(l),r=i.move("[^"),a=n.enter("footnoteReference"),o=n.enter("reference");return r+=i.move(n.safe(n.associationId(t),{after:"]",before:r})),o(),a(),r+=i.move("]"),r}function Cm(){return{enter:{gfmFootnoteCallString:Y2,gfmFootnoteCall:V2,gfmFootnoteDefinitionLabelString:X2,gfmFootnoteDefinition:Q2},exit:{gfmFootnoteCallString:Z2,gfmFootnoteCall:I2,gfmFootnoteDefinitionLabelString:F2,gfmFootnoteDefinition:K2}}}function Mm(t){let e=!1;return t&&t.firstLineBlank&&(e=!0),{handlers:{footnoteDefinition:n,footnoteReference:l0},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(l,i,r,a){let o=r.createTracker(a),u=o.move("[^"),c=r.enter("footnoteDefinition"),s=r.enter("label");return u+=o.move(r.safe(r.associationId(l),{before:u,after:"]"})),s(),u+=o.move("]:"),l.children&&l.children.length>0&&(o.shift(4),u+=o.move((e?`
`:" ")+r.indentLines(r.containerFlow(l,o.current()),e?i0:W2))),c(),u}}function W2(t,e,n){return e===0?t:i0(t,e,n)}function i0(t,e,n){return(n?"":"    ")+t}var P2=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];r0.peek=eT;function Dm(){return{canContainEols:["delete"],enter:{strikethrough:$2},exit:{strikethrough:tT}}}function Om(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:P2}],handlers:{delete:r0}}}function $2(t){this.enter({type:"delete",children:[]},t)}function tT(t){this.exit(t)}function r0(t,e,n,l){let i=n.createTracker(l),r=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(t,Et(C({},i.current()),{before:a,after:"~"})),a+=i.move("~~"),r(),a}function eT(){return"~"}function nT(t){return t.length}function o0(t,e){let n=e||{},l=(n.align||[]).concat(),i=n.stringLength||nT,r=[],a=[],o=[],u=[],c=0,s=-1;for(;++s<t.length;){let v=[],E=[],h=-1;for(t[s].length>c&&(c=t[s].length);++h<t[s].length;){let d=lT(t[s][h]);if(n.alignDelimiters!==!1){let g=i(d);E[h]=g,(u[h]===void 0||g>u[h])&&(u[h]=g)}v.push(d)}a[s]=v,o[s]=E}let f=-1;if(typeof l=="object"&&"length"in l)for(;++f<c;)r[f]=a0(l[f]);else{let v=a0(l);for(;++f<c;)r[f]=v}f=-1;let p=[],m=[];for(;++f<c;){let v=r[f],E="",h="";v===99?(E=":",h=":"):v===108?E=":":v===114&&(h=":");let d=n.alignDelimiters===!1?1:Math.max(1,u[f]-E.length-h.length),g=E+"-".repeat(d)+h;n.alignDelimiters!==!1&&(d=E.length+d+h.length,d>u[f]&&(u[f]=d),m[f]=d),p[f]=g}a.splice(1,0,p),o.splice(1,0,m),s=-1;let y=[];for(;++s<a.length;){let v=a[s],E=o[s];f=-1;let h=[];for(;++f<c;){let d=v[f]||"",g="",S="";if(n.alignDelimiters!==!1){let z=u[f]-(E[f]||0),w=r[f];w===114?g=" ".repeat(z):w===99?z%2?(g=" ".repeat(z/2+.5),S=" ".repeat(z/2-.5)):(g=" ".repeat(z/2),S=g):S=" ".repeat(z)}n.delimiterStart!==!1&&!f&&h.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&d==="")&&(n.delimiterStart!==!1||f)&&h.push(" "),n.alignDelimiters!==!1&&h.push(g),h.push(d),n.alignDelimiters!==!1&&h.push(S),n.padding!==!1&&h.push(" "),(n.delimiterEnd!==!1||f!==c-1)&&h.push("|")}y.push(n.delimiterEnd===!1?h.join("").replace(/ +$/,""):h.join(""))}return y.join(`
`)}function lT(t){return t==null?"":String(t)}function a0(t){let e=typeof t=="string"?t.codePointAt(0):0;return e===67||e===99?99:e===76||e===108?108:e===82||e===114?114:0}function u0(t,e,n,l){let i=n.enter("blockquote"),r=n.createTracker(l);r.move("> "),r.shift(2);let a=n.indentLines(n.containerFlow(t,r.current()),iT);return i(),a}function iT(t,e,n){return">"+(n?"":" ")+t}function s0(t,e){return c0(t,e.inConstruct,!0)&&!c0(t,e.notInConstruct,!1)}function c0(t,e,n){if(typeof e=="string"&&(e=[e]),!e||e.length===0)return n;let l=-1;for(;++l<e.length;)if(t.includes(e[l]))return!0;return!1}function Rm(t,e,n,l){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&s0(n.stack,n.unsafe[i]))return/[ \t]/.test(l.before)?"":" ";return`\\
`}function f0(t,e){let n=String(t),l=n.indexOf(e),i=l,r=0,a=0;if(typeof e!="string")throw new TypeError("Expected substring");for(;l!==-1;)l===i?++r>a&&(a=r):r=1,i=l+e.length,l=n.indexOf(e,i);return a}function m0(t,e){return!!(e.options.fences===!1&&t.value&&!t.lang&&/[^ \r\n]/.test(t.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(t.value))}function p0(t){let e=t.options.fence||"`";if(e!=="`"&&e!=="~")throw new Error("Cannot serialize code with `"+e+"` for `options.fence`, expected `` ` `` or `~`");return e}function h0(t,e,n,l){let i=p0(n),r=t.value||"",a=i==="`"?"GraveAccent":"Tilde";if(m0(t,n)){let f=n.enter("codeIndented"),p=n.indentLines(r,rT);return f(),p}let o=n.createTracker(l),u=i.repeat(Math.max(f0(r,i)+1,3)),c=n.enter("codeFenced"),s=o.move(u);if(t.lang){let f=n.enter(`codeFencedLang${a}`);s+=o.move(n.safe(t.lang,C({before:s,after:" ",encode:["`"]},o.current()))),f()}if(t.lang&&t.meta){let f=n.enter(`codeFencedMeta${a}`);s+=o.move(" "),s+=o.move(n.safe(t.meta,C({before:s,after:`
`,encode:["`"]},o.current()))),f()}return s+=o.move(`
`),r&&(s+=o.move(r+`
`)),s+=o.move(u),c(),s}function rT(t,e,n){return(n?"":"    ")+t}function Fi(t){let e=t.options.quote||'"';if(e!=='"'&&e!=="'")throw new Error("Cannot serialize title with `"+e+"` for `options.quote`, expected `\"`, or `'`");return e}function d0(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("definition"),o=n.enter("label"),u=n.createTracker(l),c=u.move("[");return c+=u.move(n.safe(n.associationId(t),C({before:c,after:"]"},u.current()))),c+=u.move("]: "),o(),!t.url||/[\0- \u007F]/.test(t.url)?(o=n.enter("destinationLiteral"),c+=u.move("<"),c+=u.move(n.safe(t.url,C({before:c,after:">"},u.current()))),c+=u.move(">")):(o=n.enter("destinationRaw"),c+=u.move(n.safe(t.url,C({before:c,after:t.title?" ":`
`},u.current())))),o(),t.title&&(o=n.enter(`title${r}`),c+=u.move(" "+i),c+=u.move(n.safe(t.title,C({before:c,after:i},u.current()))),c+=u.move(i),o()),a(),c}function g0(t){let e=t.options.emphasis||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize emphasis with `"+e+"` for `options.emphasis`, expected `*`, or `_`");return e}function sl(t){return"&#x"+t.toString(16).toUpperCase()+";"}function Ki(t,e,n){let l=zn(t),i=zn(e);return l===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:l===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}_m.peek=aT;function _m(t,e,n,l){let i=g0(n),r=n.enter("emphasis"),a=n.createTracker(l),o=a.move(i),u=a.move(n.containerPhrasing(t,C({after:i,before:o},a.current()))),c=u.charCodeAt(0),s=Ki(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(u=sl(c)+u.slice(1));let f=u.charCodeAt(u.length-1),p=Ki(l.after.charCodeAt(0),f,i);p.inside&&(u=u.slice(0,-1)+sl(f));let m=a.move(i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},o+u+m}function aT(t,e,n){return n.options.emphasis||"*"}function y0(t,e){let n=!1;return Yl(t,function(l){if("value"in l&&/\r?\n|\r/.test(l.value)||l.type==="break")return n=!0,Gl}),!!((!t.depth||t.depth<3)&&Ll(t)&&(e.options.setext||n))}function b0(t,e,n,l){let i=Math.max(Math.min(6,t.depth||1),1),r=n.createTracker(l);if(y0(t,n)){let s=n.enter("headingSetext"),f=n.enter("phrasing"),p=n.containerPhrasing(t,Et(C({},r.current()),{before:`
`,after:`
`}));return f(),s(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}let a="#".repeat(i),o=n.enter("headingAtx"),u=n.enter("phrasing");r.move(a+" ");let c=n.containerPhrasing(t,C({before:"# ",after:`
`},r.current()));return/^[\t ]/.test(c)&&(c=sl(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,n.options.closeAtx&&(c+=" "+a),u(),o(),c}Nm.peek=oT;function Nm(t){return t.value||""}function oT(){return"<"}Lm.peek=uT;function Lm(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("image"),o=n.enter("label"),u=n.createTracker(l),c=u.move("![");return c+=u.move(n.safe(t.alt,C({before:c,after:"]"},u.current()))),c+=u.move("]("),o(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(o=n.enter("destinationLiteral"),c+=u.move("<"),c+=u.move(n.safe(t.url,C({before:c,after:">"},u.current()))),c+=u.move(">")):(o=n.enter("destinationRaw"),c+=u.move(n.safe(t.url,C({before:c,after:t.title?" ":")"},u.current())))),o(),t.title&&(o=n.enter(`title${r}`),c+=u.move(" "+i),c+=u.move(n.safe(t.title,C({before:c,after:i},u.current()))),c+=u.move(i),o()),c+=u.move(")"),a(),c}function uT(){return"!"}Um.peek=cT;function Um(t,e,n,l){let i=t.referenceType,r=n.enter("imageReference"),a=n.enter("label"),o=n.createTracker(l),u=o.move("!["),c=n.safe(t.alt,C({before:u,after:"]"},o.current()));u+=o.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),C({before:u,after:"]"},o.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?u+=o.move(f+"]"):i==="shortcut"?u=u.slice(0,-1):u+=o.move("]"),u}function cT(){return"!"}Bm.peek=sT;function Bm(t,e,n){let l=t.value||"",i="`",r=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(l);)i+="`";for(/[^ \r\n]/.test(l)&&(/^[ \r\n]/.test(l)&&/[ \r\n]$/.test(l)||/^`|`$/.test(l))&&(l=" "+l+" ");++r<n.unsafe.length;){let a=n.unsafe[r],o=n.compilePattern(a),u;if(a.atBreak)for(;u=o.exec(l);){let c=u.index;l.charCodeAt(c)===10&&l.charCodeAt(c-1)===13&&c--,l=l.slice(0,c)+" "+l.slice(u.index+1)}}return i+l+i}function sT(){return"`"}function Hm(t,e){let n=Ll(t);return!!(!e.options.resourceLink&&t.url&&!t.title&&t.children&&t.children.length===1&&t.children[0].type==="text"&&(n===t.url||"mailto:"+n===t.url)&&/^[a-z][a-z+.-]+:/i.test(t.url)&&!/[\0- <>\u007F]/.test(t.url))}qm.peek=fT;function qm(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.createTracker(l),o,u;if(Hm(t,n)){let s=n.stack;n.stack=[],o=n.enter("autolink");let f=a.move("<");return f+=a.move(n.containerPhrasing(t,C({before:f,after:">"},a.current()))),f+=a.move(">"),o(),n.stack=s,f}o=n.enter("link"),u=n.enter("label");let c=a.move("[");return c+=a.move(n.containerPhrasing(t,C({before:c,after:"]("},a.current()))),c+=a.move("]("),u(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(t.url,C({before:c,after:">"},a.current()))),c+=a.move(">")):(u=n.enter("destinationRaw"),c+=a.move(n.safe(t.url,C({before:c,after:t.title?" ":")"},a.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=a.move(" "+i),c+=a.move(n.safe(t.title,C({before:c,after:i},a.current()))),c+=a.move(i),u()),c+=a.move(")"),o(),c}function fT(t,e,n){return Hm(t,n)?"<":"["}jm.peek=mT;function jm(t,e,n,l){let i=t.referenceType,r=n.enter("linkReference"),a=n.enter("label"),o=n.createTracker(l),u=o.move("["),c=n.containerPhrasing(t,C({before:u,after:"]"},o.current()));u+=o.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),C({before:u,after:"]"},o.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?u+=o.move(f+"]"):i==="shortcut"?u=u.slice(0,-1):u+=o.move("]"),u}function mT(){return"["}function Ji(t){let e=t.options.bullet||"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bullet`, expected `*`, `+`, or `-`");return e}function x0(t){let e=Ji(t),n=t.options.bulletOther;if(!n)return e==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===e)throw new Error("Expected `bullet` (`"+e+"`) and `bulletOther` (`"+n+"`) to be different");return n}function v0(t){let e=t.options.bulletOrdered||".";if(e!=="."&&e!==")")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOrdered`, expected `.` or `)`");return e}function Lu(t){let e=t.options.rule||"*";if(e!=="*"&&e!=="-"&&e!=="_")throw new Error("Cannot serialize rules with `"+e+"` for `options.rule`, expected `*`, `-`, or `_`");return e}function S0(t,e,n,l){let i=n.enter("list"),r=n.bulletCurrent,a=t.ordered?v0(n):Ji(n),o=t.ordered?a==="."?")":".":x0(n),u=e&&n.bulletLastUsed?a===n.bulletLastUsed:!1;if(!t.ordered){let s=t.children?t.children[0]:void 0;if((a==="*"||a==="-")&&s&&(!s.children||!s.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(u=!0),Lu(n)===a&&s){let f=-1;for(;++f<t.children.length;){let p=t.children[f];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){u=!0;break}}}}u&&(a=o),n.bulletCurrent=a;let c=n.containerFlow(t,l);return n.bulletLastUsed=a,n.bulletCurrent=r,i(),c}function k0(t){let e=t.options.listItemIndent||"one";if(e!=="tab"&&e!=="one"&&e!=="mixed")throw new Error("Cannot serialize items with `"+e+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return e}function w0(t,e,n,l){let i=k0(n),r=n.bulletCurrent||Ji(n);e&&e.type==="list"&&e.ordered&&(r=(typeof e.start=="number"&&e.start>-1?e.start:1)+(n.options.incrementListMarker===!1?0:e.children.indexOf(t))+r);let a=r.length+1;(i==="tab"||i==="mixed"&&(e&&e.type==="list"&&e.spread||t.spread))&&(a=Math.ceil(a/4)*4);let o=n.createTracker(l);o.move(r+" ".repeat(a-r.length)),o.shift(a);let u=n.enter("listItem"),c=n.indentLines(n.containerFlow(t,o.current()),s);return u(),c;function s(f,p,m){return p?(m?"":" ".repeat(a))+f:(m?r:r+" ".repeat(a-r.length))+f}}function E0(t,e,n,l){let i=n.enter("paragraph"),r=n.enter("phrasing"),a=n.containerPhrasing(t,l);return r(),i(),a}var Gm=cl(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function T0(t,e,n,l){return(t.children.some(function(a){return Gm(a)})?n.containerPhrasing:n.containerFlow).call(n,t,l)}function A0(t){let e=t.options.strong||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize strong with `"+e+"` for `options.strong`, expected `*`, or `_`");return e}Ym.peek=pT;function Ym(t,e,n,l){let i=A0(n),r=n.enter("strong"),a=n.createTracker(l),o=a.move(i+i),u=a.move(n.containerPhrasing(t,C({after:i,before:o},a.current()))),c=u.charCodeAt(0),s=Ki(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(u=sl(c)+u.slice(1));let f=u.charCodeAt(u.length-1),p=Ki(l.after.charCodeAt(0),f,i);p.inside&&(u=u.slice(0,-1)+sl(f));let m=a.move(i+i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},o+u+m}function pT(t,e,n){return n.options.strong||"*"}function z0(t,e,n,l){return n.safe(t.value,l)}function C0(t){let e=t.options.ruleRepetition||3;if(e<3)throw new Error("Cannot serialize rules with repetition `"+e+"` for `options.ruleRepetition`, expected `3` or more");return e}function M0(t,e,n){let l=(Lu(n)+(n.options.ruleSpaces?" ":"")).repeat(C0(n));return n.options.ruleSpaces?l.slice(0,-1):l}var Ea={blockquote:u0,break:Rm,code:h0,definition:d0,emphasis:_m,hardBreak:Rm,heading:b0,html:Nm,image:Lm,imageReference:Um,inlineCode:Bm,link:qm,linkReference:jm,list:S0,listItem:w0,paragraph:E0,root:T0,strong:Ym,text:z0,thematicBreak:M0};function Xm(){return{enter:{table:hT,tableData:D0,tableHeader:D0,tableRow:gT},exit:{codeText:yT,table:dT,tableData:Vm,tableHeader:Vm,tableRow:Vm}}}function hT(t){let e=t._align;this.enter({type:"table",align:e.map(function(n){return n==="none"?null:n}),children:[]},t),this.data.inTable=!0}function dT(t){this.exit(t),this.data.inTable=void 0}function gT(t){this.enter({type:"tableRow",children:[]},t)}function Vm(t){this.exit(t)}function D0(t){this.enter({type:"tableCell",children:[]},t)}function yT(t){let e=this.resume();this.data.inTable&&(e=e.replace(/\\([\\|])/g,bT));let n=this.stack[this.stack.length-1];n.type,n.value=e,this.exit(t)}function bT(t,e){return e==="|"?e:t}function Qm(t){let e=t||{},n=e.tableCellPadding,l=e.tablePipeAlign,i=e.stringLength,r=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:a,tableCell:u,tableRow:o}};function a(m,y,v,E){return c(s(m,v,E),m.align)}function o(m,y,v,E){let h=f(m,v,E),d=c([h]);return d.slice(0,d.indexOf(`
`))}function u(m,y,v,E){let h=v.enter("tableCell"),d=v.enter("phrasing"),g=v.containerPhrasing(m,Et(C({},E),{before:r,after:r}));return d(),h(),g}function c(m,y){return o0(m,{align:y,alignDelimiters:l,padding:n,stringLength:i})}function s(m,y,v){let E=m.children,h=-1,d=[],g=y.enter("table");for(;++h<E.length;)d[h]=f(E[h],y,v);return g(),d}function f(m,y,v){let E=m.children,h=-1,d=[],g=y.enter("tableRow");for(;++h<E.length;)d[h]=u(E[h],m,y,v);return g(),d}function p(m,y,v){let E=Ea.inlineCode(m,y,v);return v.stack.includes("tableCell")&&(E=E.replace(/\|/g,"\\$&")),E}}function Zm(){return{exit:{taskListCheckValueChecked:O0,taskListCheckValueUnchecked:O0,paragraph:xT}}}function Im(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:vT}}}function O0(t){let e=this.stack[this.stack.length-2];e.type,e.checked=t.type==="taskListCheckValueChecked"}function xT(t){let e=this.stack[this.stack.length-2];if(e&&e.type==="listItem"&&typeof e.checked=="boolean"){let n=this.stack[this.stack.length-1];n.type;let l=n.children[0];if(l&&l.type==="text"){let i=e.children,r=-1,a;for(;++r<i.length;){let o=i[r];if(o.type==="paragraph"){a=o;break}}a===n&&(l.value=l.value.slice(1),l.value.length===0?n.children.shift():n.position&&l.position&&typeof l.position.start.offset=="number"&&(l.position.start.column++,l.position.start.offset++,n.position.start=Object.assign({},l.position.start)))}}this.exit(t)}function vT(t,e,n,l){let i=t.children[0],r=typeof t.checked=="boolean"&&i&&i.type==="paragraph",a="["+(t.checked?"x":" ")+"] ",o=n.createTracker(l);r&&o.move(a);let u=Ea.listItem(t,e,n,C(C({},l),o.current()));return r&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,c)),u;function c(s){return s+a}}function Fm(){return[Am(),Cm(),Dm(),Xm(),Zm()]}function Km(t){return{extensions:[zm(),Mm(t),Om(),Qm(t),Im()]}}var ST={tokenize:AT,partial:!0},R0={tokenize:zT,partial:!0},_0={tokenize:CT,partial:!0},N0={tokenize:MT,partial:!0},kT={tokenize:DT,partial:!0},L0={name:"wwwAutolink",tokenize:ET,previous:B0},U0={name:"protocolAutolink",tokenize:TT,previous:H0},Cn={name:"emailAutolink",tokenize:wT,previous:q0},an={};function Wm(){return{text:an}}var Xl=48;for(;Xl<123;)an[Xl]=Cn,Xl++,Xl===58?Xl=65:Xl===91&&(Xl=97);an[43]=Cn;an[45]=Cn;an[46]=Cn;an[95]=Cn;an[72]=[Cn,U0];an[104]=[Cn,U0];an[87]=[Cn,L0];an[119]=[Cn,L0];function wT(t,e,n){let l=this,i,r;return a;function a(f){return!Jm(f)||!q0.call(l,l.previous)||Pm(l.events)?n(f):(t.enter("literalAutolink"),t.enter("literalAutolinkEmail"),o(f))}function o(f){return Jm(f)?(t.consume(f),o):f===64?(t.consume(f),u):n(f)}function u(f){return f===46?t.check(kT,s,c)(f):f===45||f===95||Mt(f)?(r=!0,t.consume(f),u):s(f)}function c(f){return t.consume(f),i=!0,u}function s(f){return r&&i&&Gt(l.previous)?(t.exit("literalAutolinkEmail"),t.exit("literalAutolink"),e(f)):n(f)}}function ET(t,e,n){let l=this;return i;function i(a){return a!==87&&a!==119||!B0.call(l,l.previous)||Pm(l.events)?n(a):(t.enter("literalAutolink"),t.enter("literalAutolinkWww"),t.check(ST,t.attempt(R0,t.attempt(_0,r),n),n)(a))}function r(a){return t.exit("literalAutolinkWww"),t.exit("literalAutolink"),e(a)}}function TT(t,e,n){let l=this,i="",r=!1;return a;function a(f){return(f===72||f===104)&&H0.call(l,l.previous)&&!Pm(l.events)?(t.enter("literalAutolink"),t.enter("literalAutolinkHttp"),i+=String.fromCodePoint(f),t.consume(f),o):n(f)}function o(f){if(Gt(f)&&i.length<5)return i+=String.fromCodePoint(f),t.consume(f),o;if(f===58){let p=i.toLowerCase();if(p==="http"||p==="https")return t.consume(f),u}return n(f)}function u(f){return f===47?(t.consume(f),r?c:(r=!0,u)):n(f)}function c(f){return f===null||Ul(f)||P(f)||ln(f)||Bl(f)?n(f):t.attempt(R0,t.attempt(_0,s),n)(f)}function s(f){return t.exit("literalAutolinkHttp"),t.exit("literalAutolink"),e(f)}}function AT(t,e,n){let l=0;return i;function i(a){return(a===87||a===119)&&l<3?(l++,t.consume(a),i):a===46&&l===3?(t.consume(a),r):n(a)}function r(a){return a===null?n(a):e(a)}}function zT(t,e,n){let l,i,r;return a;function a(c){return c===46||c===95?t.check(N0,u,o)(c):c===null||P(c)||ln(c)||c!==45&&Bl(c)?u(c):(r=!0,t.consume(c),a)}function o(c){return c===95?l=!0:(i=l,l=void 0),t.consume(c),a}function u(c){return i||l||!r?n(c):e(c)}}function CT(t,e){let n=0,l=0;return i;function i(a){return a===40?(n++,t.consume(a),i):a===41&&l<n?r(a):a===33||a===34||a===38||a===39||a===41||a===42||a===44||a===46||a===58||a===59||a===60||a===63||a===93||a===95||a===126?t.check(N0,e,r)(a):a===null||P(a)||ln(a)?e(a):(t.consume(a),i)}function r(a){return a===41&&l++,t.consume(a),i}}function MT(t,e,n){return l;function l(o){return o===33||o===34||o===39||o===41||o===42||o===44||o===46||o===58||o===59||o===63||o===95||o===126?(t.consume(o),l):o===38?(t.consume(o),r):o===93?(t.consume(o),i):o===60||o===null||P(o)||ln(o)?e(o):n(o)}function i(o){return o===null||o===40||o===91||P(o)||ln(o)?e(o):l(o)}function r(o){return Gt(o)?a(o):n(o)}function a(o){return o===59?(t.consume(o),l):Gt(o)?(t.consume(o),a):n(o)}}function DT(t,e,n){return l;function l(r){return t.consume(r),i}function i(r){return Mt(r)?n(r):e(r)}}function B0(t){return t===null||t===40||t===42||t===95||t===91||t===93||t===126||P(t)}function H0(t){return!Gt(t)}function q0(t){return!(t===47||Jm(t))}function Jm(t){return t===43||t===45||t===46||t===95||Mt(t)}function Pm(t){let e=t.length,n=!1;for(;e--;){let l=t[e][1];if((l.type==="labelLink"||l.type==="labelImage")&&!l._balanced){n=!0;break}if(l._gfmAutolinkLiteralWalkedInto){n=!1;break}}return t.length>0&&!n&&(t[t.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}var OT={tokenize:HT,partial:!0};function $m(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:LT,continuation:{tokenize:UT},exit:BT}},text:{91:{name:"gfmFootnoteCall",tokenize:NT},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:RT,resolveTo:_T}}}}function RT(t,e,n){let l=this,i=l.events.length,r=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),a;for(;i--;){let u=l.events[i][1];if(u.type==="labelImage"){a=u;break}if(u.type==="gfmFootnoteCall"||u.type==="labelLink"||u.type==="label"||u.type==="image"||u.type==="link")break}return o;function o(u){if(!a||!a._balanced)return n(u);let c=ne(l.sliceSerialize({start:a.end,end:l.now()}));return c.codePointAt(0)!==94||!r.includes(c.slice(1))?n(u):(t.enter("gfmFootnoteCallLabelMarker"),t.consume(u),t.exit("gfmFootnoteCallLabelMarker"),e(u))}}function _T(t,e){let n=t.length,l;for(;n--;)if(t[n][1].type==="labelImage"&&t[n][0]==="enter"){l=t[n][1];break}t[n+1][1].type="data",t[n+3][1].type="gfmFootnoteCallLabelMarker";let i={type:"gfmFootnoteCall",start:Object.assign({},t[n+3][1].start),end:Object.assign({},t[t.length-1][1].end)},r={type:"gfmFootnoteCallMarker",start:Object.assign({},t[n+3][1].end),end:Object.assign({},t[n+3][1].end)};r.end.column++,r.end.offset++,r.end._bufferIndex++;let a={type:"gfmFootnoteCallString",start:Object.assign({},r.end),end:Object.assign({},t[t.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},u=[t[n+1],t[n+2],["enter",i,e],t[n+3],t[n+4],["enter",r,e],["exit",r,e],["enter",a,e],["enter",o,e],["exit",o,e],["exit",a,e],t[t.length-2],t[t.length-1],["exit",i,e]];return t.splice(n,t.length-n+1,...u),t}function NT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r=0,a;return o;function o(f){return t.enter("gfmFootnoteCall"),t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),u}function u(f){return f!==94?n(f):(t.enter("gfmFootnoteCallMarker"),t.consume(f),t.exit("gfmFootnoteCallMarker"),t.enter("gfmFootnoteCallString"),t.enter("chunkString").contentType="string",c)}function c(f){if(r>999||f===93&&!a||f===null||f===91||P(f))return n(f);if(f===93){t.exit("chunkString");let p=t.exit("gfmFootnoteCallString");return i.includes(ne(l.sliceSerialize(p)))?(t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),t.exit("gfmFootnoteCall"),e):n(f)}return P(f)||(a=!0),r++,t.consume(f),f===92?s:c}function s(f){return f===91||f===92||f===93?(t.consume(f),r++,c):c(f)}}function LT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r,a=0,o;return u;function u(y){return t.enter("gfmFootnoteDefinition")._container=!0,t.enter("gfmFootnoteDefinitionLabel"),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),c}function c(y){return y===94?(t.enter("gfmFootnoteDefinitionMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionMarker"),t.enter("gfmFootnoteDefinitionLabelString"),t.enter("chunkString").contentType="string",s):n(y)}function s(y){if(a>999||y===93&&!o||y===null||y===91||P(y))return n(y);if(y===93){t.exit("chunkString");let v=t.exit("gfmFootnoteDefinitionLabelString");return r=ne(l.sliceSerialize(v)),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),t.exit("gfmFootnoteDefinitionLabel"),p}return P(y)||(o=!0),a++,t.consume(y),y===92?f:s}function f(y){return y===91||y===92||y===93?(t.consume(y),a++,s):s(y)}function p(y){return y===58?(t.enter("definitionMarker"),t.consume(y),t.exit("definitionMarker"),i.includes(r)||i.push(r),Y(t,m,"gfmFootnoteDefinitionWhitespace")):n(y)}function m(y){return e(y)}}function UT(t,e,n){return t.check(rn,e,t.attempt(OT,e,n))}function BT(t){t.exit("gfmFootnoteDefinition")}function HT(t,e,n){let l=this;return Y(t,i,"gfmFootnoteDefinitionIndent",5);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="gfmFootnoteDefinitionIndent"&&a[2].sliceSerialize(a[1],!0).length===4?e(r):n(r)}}function tp(t){let n=(t||{}).singleTilde,l={name:"strikethrough",tokenize:r,resolveAll:i};return n==null&&(n=!0),{text:{126:l},insideSpan:{null:[l]},attentionMarkers:{null:[126]}};function i(a,o){let u=-1;for(;++u<a.length;)if(a[u][0]==="enter"&&a[u][1].type==="strikethroughSequenceTemporary"&&a[u][1]._close){let c=u;for(;c--;)if(a[c][0]==="exit"&&a[c][1].type==="strikethroughSequenceTemporary"&&a[c][1]._open&&a[u][1].end.offset-a[u][1].start.offset===a[c][1].end.offset-a[c][1].start.offset){a[u][1].type="strikethroughSequence",a[c][1].type="strikethroughSequence";let s={type:"strikethrough",start:Object.assign({},a[c][1].start),end:Object.assign({},a[u][1].end)},f={type:"strikethroughText",start:Object.assign({},a[c][1].end),end:Object.assign({},a[u][1].start)},p=[["enter",s,o],["enter",a[c][1],o],["exit",a[c][1],o],["enter",f,o]],m=o.parser.constructs.insideSpan.null;m&&Nt(p,p.length,0,ol(m,a.slice(c+1,u),o)),Nt(p,p.length,0,[["exit",f,o],["enter",a[u][1],o],["exit",a[u][1],o],["exit",s,o]]),Nt(a,c-1,u-c+3,p),u=c+p.length-2;break}}for(u=-1;++u<a.length;)a[u][1].type==="strikethroughSequenceTemporary"&&(a[u][1].type="data");return a}function r(a,o,u){let c=this.previous,s=this.events,f=0;return p;function p(y){return c===126&&s[s.length-1][1].type!=="characterEscape"?u(y):(a.enter("strikethroughSequenceTemporary"),m(y))}function m(y){let v=zn(c);if(y===126)return f>1?u(y):(a.consume(y),f++,m);if(f<2&&!n)return u(y);let E=a.exit("strikethroughSequenceTemporary"),h=zn(y);return E._open=!h||h===2&&!!v,E._close=!v||v===2&&!!h,o(y)}}}var Uu=class{constructor(){this.map=[]}add(e,n,l){qT(this,e,n,l)}consume(e){if(this.map.sort(function(r,a){return r[0]-a[0]}),this.map.length===0)return;let n=this.map.length,l=[];for(;n>0;)n-=1,l.push(e.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),e.length=this.map[n][0];l.push(e.slice()),e.length=0;let i=l.pop();for(;i;){for(let r of i)e.push(r);i=l.pop()}this.map.length=0}};function qT(t,e,n,l){let i=0;if(!(n===0&&l.length===0)){for(;i<t.map.length;){if(t.map[i][0]===e){t.map[i][1]+=n,t.map[i][2].push(...l);return}i+=1}t.map.push([e,n,l])}}function j0(t,e){let n=!1,l=[];for(;e<t.length;){let i=t[e];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&l.push(t[e+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(t[e-1][1].type==="tableDelimiterMarker"){let r=l.length-1;l[r]=l[r]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);e+=1}return l}function ep(){return{flow:{null:{name:"table",tokenize:jT,resolveAll:GT}}}}function jT(t,e,n){let l=this,i=0,r=0,a;return o;function o(k){let it=l.events.length-1;for(;it>-1;){let j=l.events[it][1].type;if(j==="lineEnding"||j==="linePrefix")it--;else break}let L=it>-1?l.events[it][1].type:null,_=L==="tableHead"||L==="tableRow"?w:u;return _===w&&l.parser.lazy[l.now().line]?n(k):_(k)}function u(k){return t.enter("tableHead"),t.enter("tableRow"),c(k)}function c(k){return k===124||(a=!0,r+=1),s(k)}function s(k){return k===null?n(k):N(k)?r>1?(r=0,l.interrupt=!0,t.exit("tableRow"),t.enter("lineEnding"),t.consume(k),t.exit("lineEnding"),m):n(k):V(k)?Y(t,s,"whitespace")(k):(r+=1,a&&(a=!1,i+=1),k===124?(t.enter("tableCellDivider"),t.consume(k),t.exit("tableCellDivider"),a=!0,s):(t.enter("data"),f(k)))}function f(k){return k===null||k===124||P(k)?(t.exit("data"),s(k)):(t.consume(k),k===92?p:f)}function p(k){return k===92||k===124?(t.consume(k),f):f(k)}function m(k){return l.interrupt=!1,l.parser.lazy[l.now().line]?n(k):(t.enter("tableDelimiterRow"),a=!1,V(k)?Y(t,y,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(k):y(k))}function y(k){return k===45||k===58?E(k):k===124?(a=!0,t.enter("tableCellDivider"),t.consume(k),t.exit("tableCellDivider"),v):z(k)}function v(k){return V(k)?Y(t,E,"whitespace")(k):E(k)}function E(k){return k===58?(r+=1,a=!0,t.enter("tableDelimiterMarker"),t.consume(k),t.exit("tableDelimiterMarker"),h):k===45?(r+=1,h(k)):k===null||N(k)?S(k):z(k)}function h(k){return k===45?(t.enter("tableDelimiterFiller"),d(k)):z(k)}function d(k){return k===45?(t.consume(k),d):k===58?(a=!0,t.exit("tableDelimiterFiller"),t.enter("tableDelimiterMarker"),t.consume(k),t.exit("tableDelimiterMarker"),g):(t.exit("tableDelimiterFiller"),g(k))}function g(k){return V(k)?Y(t,S,"whitespace")(k):S(k)}function S(k){return k===124?y(k):k===null||N(k)?!a||i!==r?z(k):(t.exit("tableDelimiterRow"),t.exit("tableHead"),e(k)):z(k)}function z(k){return n(k)}function w(k){return t.enter("tableRow"),D(k)}function D(k){return k===124?(t.enter("tableCellDivider"),t.consume(k),t.exit("tableCellDivider"),D):k===null||N(k)?(t.exit("tableRow"),e(k)):V(k)?Y(t,D,"whitespace")(k):(t.enter("data"),T(k))}function T(k){return k===null||k===124||P(k)?(t.exit("data"),D(k)):(t.consume(k),k===92?q:T)}function q(k){return k===92||k===124?(t.consume(k),T):T(k)}}function GT(t,e){let n=-1,l=!0,i=0,r=[0,0,0,0],a=[0,0,0,0],o=!1,u=0,c,s,f,p=new Uu;for(;++n<t.length;){let m=t[n],y=m[1];m[0]==="enter"?y.type==="tableHead"?(o=!1,u!==0&&(G0(p,e,u,c,s),s=void 0,u=0),c={type:"table",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",c,e]])):y.type==="tableRow"||y.type==="tableDelimiterRow"?(l=!0,f=void 0,r=[0,0,0,0],a=[0,n+1,0,0],o&&(o=!1,s={type:"tableBody",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",s,e]])),i=y.type==="tableDelimiterRow"?2:s?3:1):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")?(l=!1,a[2]===0&&(r[1]!==0&&(a[0]=a[1],f=Bu(p,e,r,i,void 0,f),r=[0,0,0,0]),a[2]=n)):y.type==="tableCellDivider"&&(l?l=!1:(r[1]!==0&&(a[0]=a[1],f=Bu(p,e,r,i,void 0,f)),r=a,a=[r[1],n,0,0])):y.type==="tableHead"?(o=!0,u=n):y.type==="tableRow"||y.type==="tableDelimiterRow"?(u=n,r[1]!==0?(a[0]=a[1],f=Bu(p,e,r,i,n,f)):a[1]!==0&&(f=Bu(p,e,a,i,n,f)),i=0):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")&&(a[3]=n)}for(u!==0&&G0(p,e,u,c,s),p.consume(e.events),n=-1;++n<e.events.length;){let m=e.events[n];m[0]==="enter"&&m[1].type==="table"&&(m[1]._align=j0(e.events,n))}return t}function Bu(t,e,n,l,i,r){let a=l===1?"tableHeader":l===2?"tableDelimiter":"tableData",o="tableContent";n[0]!==0&&(r.end=Object.assign({},Wi(e.events,n[0])),t.add(n[0],0,[["exit",r,e]]));let u=Wi(e.events,n[1]);if(r={type:a,start:Object.assign({},u),end:Object.assign({},u)},t.add(n[1],0,[["enter",r,e]]),n[2]!==0){let c=Wi(e.events,n[2]),s=Wi(e.events,n[3]),f={type:o,start:Object.assign({},c),end:Object.assign({},s)};if(t.add(n[2],0,[["enter",f,e]]),l!==2){let p=e.events[n[2]],m=e.events[n[3]];if(p[1].end=Object.assign({},m[1].end),p[1].type="chunkText",p[1].contentType="text",n[3]>n[2]+1){let y=n[2]+1,v=n[3]-n[2]-1;t.add(y,v,[])}}t.add(n[3]+1,0,[["exit",f,e]])}return i!==void 0&&(r.end=Object.assign({},Wi(e.events,i)),t.add(i,0,[["exit",r,e]]),r=void 0),r}function G0(t,e,n,l,i){let r=[],a=Wi(e.events,n);i&&(i.end=Object.assign({},a),r.push(["exit",i,e])),l.end=Object.assign({},a),r.push(["exit",l,e]),t.add(n+1,0,r)}function Wi(t,e){let n=t[e],l=n[0]==="enter"?"start":"end";return n[1][l]}var YT={name:"tasklistCheck",tokenize:VT};function np(){return{text:{91:YT}}}function VT(t,e,n){let l=this;return i;function i(u){return l.previous!==null||!l._gfmTasklistFirstContentOfListItem?n(u):(t.enter("taskListCheck"),t.enter("taskListCheckMarker"),t.consume(u),t.exit("taskListCheckMarker"),r)}function r(u){return P(u)?(t.enter("taskListCheckValueUnchecked"),t.consume(u),t.exit("taskListCheckValueUnchecked"),a):u===88||u===120?(t.enter("taskListCheckValueChecked"),t.consume(u),t.exit("taskListCheckValueChecked"),a):n(u)}function a(u){return u===93?(t.enter("taskListCheckMarker"),t.consume(u),t.exit("taskListCheckMarker"),t.exit("taskListCheck"),o):n(u)}function o(u){return N(u)?e(u):V(u)?t.check({tokenize:XT},e,n)(u):n(u)}}function XT(t,e,n){return Y(t,l,"whitespace");function l(i){return i===null?n(i):e(i)}}function Y0(t){return su([Wm(),$m(),tp(t),ep(),np()])}var QT={};function Hu(t){let e=this,n=t||QT,l=e.data(),i=l.micromarkExtensions||(l.micromarkExtensions=[]),r=l.fromMarkdownExtensions||(l.fromMarkdownExtensions=[]),a=l.toMarkdownExtensions||(l.toMarkdownExtensions=[]);i.push(Y0(n)),r.push(Fm()),a.push(Km(n))}function V0(t){return!t||typeof t!="string"?"":t.replace(/\\n/g,`
`).replace(/\\r\\n/g,`
`).replace(/\\r/g,`
`).replace(/([a-zA-Z])""/g,"$1").replace(/""([a-zA-Z])/g,"$1").replace(/""([!?.,;:])/g,"$1").replace(/([!?.,;:])""/g,"$1").replace(/""'/g,"'").replace(/'""/g,"'").replace(/"""/g,'"').replace(/""/g,"").replace(/\\"/g,'"').replace(/\\""/g,'"').replace(/"{2,}/g,'"').replace(/"([^"]*)""/g,'"$1"').replace(/""([^"]*)""/g,'"$1"').replace(/\\\\"/g,'"').replace(/"\s*"/g,'"').replace(/([a-zA-Z])"([a-zA-Z])/g,"$1'$2").replace(/\s+"/g,' "').replace(/"\s+/g,'" ').trim().replace(/^["']+(.*)["']+$/,"$1").replace(/^["']\s*([\s\S]*?)\s*["']$/,"$1").replace(/^["']+\s*/,"").replace(/\s*["']+$/,"").trim()}function X0(t){var e,n,l="";if(typeof t=="string"||typeof t=="number")l+=t;else if(typeof t=="object")if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(n=X0(t[e]))&&(l&&(l+=" "),l+=n)}else for(n in t)t[n]&&(l&&(l+=" "),l+=n);return l}function Q0(){for(var t,e,n=0,l="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=X0(t))&&(l&&(l+=" "),l+=e);return l}var up="-",ZT=t=>{let e=FT(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:l}=t;return{getClassGroupId:a=>{let o=a.split(up);return o[0]===""&&o.length!==1&&o.shift(),J0(o,e)||IT(a)},getConflictingClassGroupIds:(a,o)=>{let u=n[a]||[];return o&&l[a]?[...u,...l[a]]:u}}},J0=(t,e)=>{var a;if(t.length===0)return e.classGroupId;let n=t[0],l=e.nextPart.get(n),i=l?J0(t.slice(1),l):void 0;if(i)return i;if(e.validators.length===0)return;let r=t.join(up);return(a=e.validators.find(({validator:o})=>o(r)))==null?void 0:a.classGroupId},Z0=/^\[(.+)\]$/,IT=t=>{if(Z0.test(t)){let e=Z0.exec(t)[1],n=e==null?void 0:e.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}},FT=t=>{let{theme:e,classGroups:n}=t,l={nextPart:new Map,validators:[]};for(let i in n)rp(n[i],l,i,e);return l},rp=(t,e,n,l)=>{t.forEach(i=>{if(typeof i=="string"){let r=i===""?e:I0(e,i);r.classGroupId=n;return}if(typeof i=="function"){if(KT(i)){rp(i(l),e,n,l);return}e.validators.push({validator:i,classGroupId:n});return}Object.entries(i).forEach(([r,a])=>{rp(a,I0(e,r),n,l)})})},I0=(t,e)=>{let n=t;return e.split(up).forEach(l=>{n.nextPart.has(l)||n.nextPart.set(l,{nextPart:new Map,validators:[]}),n=n.nextPart.get(l)}),n},KT=t=>t.isThemeGetter,JT=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,l=new Map,i=(r,a)=>{n.set(r,a),e++,e>t&&(e=0,l=n,n=new Map)};return{get(r){let a=n.get(r);if(a!==void 0)return a;if((a=l.get(r))!==void 0)return i(r,a),a},set(r,a){n.has(r)?n.set(r,a):i(r,a)}}},ap="!",op=":",WT=op.length,PT=t=>{let{prefix:e,experimentalParseClassName:n}=t,l=i=>{let r=[],a=0,o=0,u=0,c;for(let y=0;y<i.length;y++){let v=i[y];if(a===0&&o===0){if(v===op){r.push(i.slice(u,y)),u=y+WT;continue}if(v==="/"){c=y;continue}}v==="["?a++:v==="]"?a--:v==="("?o++:v===")"&&o--}let s=r.length===0?i:i.substring(u),f=$T(s),p=f!==s,m=c&&c>u?c-u:void 0;return{modifiers:r,hasImportantModifier:p,baseClassName:f,maybePostfixModifierPosition:m}};if(e){let i=e+op,r=l;l=a=>a.startsWith(i)?r(a.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(n){let i=l;l=r=>n({className:r,parseClassName:i})}return l},$T=t=>t.endsWith(ap)?t.substring(0,t.length-1):t.startsWith(ap)?t.substring(1):t,tA=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;let i=[],r=[];return l.forEach(a=>{a[0]==="["||e[a]?(i.push(...r.sort(),a),r=[]):r.push(a)}),i.push(...r.sort()),i}},eA=t=>C({cache:JT(t.cacheSize),parseClassName:PT(t),sortModifiers:tA(t)},ZT(t)),nA=/\s+/,lA=(t,e)=>{let{parseClassName:n,getClassGroupId:l,getConflictingClassGroupIds:i,sortModifiers:r}=e,a=[],o=t.trim().split(nA),u="";for(let c=o.length-1;c>=0;c-=1){let s=o[c],{isExternal:f,modifiers:p,hasImportantModifier:m,baseClassName:y,maybePostfixModifierPosition:v}=n(s);if(f){u=s+(u.length>0?" "+u:u);continue}let E=!!v,h=l(E?y.substring(0,v):y);if(!h){if(!E){u=s+(u.length>0?" "+u:u);continue}if(h=l(y),!h){u=s+(u.length>0?" "+u:u);continue}E=!1}let d=r(p).join(":"),g=m?d+ap:d,S=g+h;if(a.includes(S))continue;a.push(S);let z=i(h,E);for(let w=0;w<z.length;++w){let D=z[w];a.push(g+D)}u=s+(u.length>0?" "+u:u)}return u};function iA(){let t=0,e,n,l="";for(;t<arguments.length;)(e=arguments[t++])&&(n=W0(e))&&(l&&(l+=" "),l+=n);return l}var W0=t=>{if(typeof t=="string")return t;let e,n="";for(let l=0;l<t.length;l++)t[l]&&(e=W0(t[l]))&&(n&&(n+=" "),n+=e);return n};function rA(t,...e){let n,l,i,r=a;function a(u){let c=e.reduce((s,f)=>f(s),t());return n=eA(c),l=n.cache.get,i=n.cache.set,r=o,o(u)}function o(u){let c=l(u);if(c)return c;let s=lA(u,n);return i(u,s),s}return function(){return r(iA.apply(null,arguments))}}var Yt=t=>{let e=n=>n[t]||[];return e.isThemeGetter=!0,e},P0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,$0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,aA=/^\d+\/\d+$/,oA=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,uA=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cA=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,sA=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fA=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Pi=t=>aA.test(t),W=t=>!!t&&!Number.isNaN(Number(t)),fl=t=>!!t&&Number.isInteger(Number(t)),lp=t=>t.endsWith("%")&&W(t.slice(0,-1)),Mn=t=>oA.test(t),mA=()=>!0,pA=t=>uA.test(t)&&!cA.test(t),tx=()=>!1,hA=t=>sA.test(t),dA=t=>fA.test(t),gA=t=>!B(t)&&!H(t),yA=t=>$i(t,lx,tx),B=t=>P0.test(t),Ql=t=>$i(t,ix,pA),ip=t=>$i(t,kA,W),F0=t=>$i(t,ex,tx),bA=t=>$i(t,nx,dA),qu=t=>$i(t,rx,hA),H=t=>$0.test(t),Ta=t=>tr(t,ix),xA=t=>tr(t,wA),K0=t=>tr(t,ex),vA=t=>tr(t,lx),SA=t=>tr(t,nx),ju=t=>tr(t,rx,!0),$i=(t,e,n)=>{let l=P0.exec(t);return l?l[1]?e(l[1]):n(l[2]):!1},tr=(t,e,n=!1)=>{let l=$0.exec(t);return l?l[1]?e(l[1]):n:!1},ex=t=>t==="position"||t==="percentage",nx=t=>t==="image"||t==="url",lx=t=>t==="length"||t==="size"||t==="bg-size",ix=t=>t==="length",kA=t=>t==="number",wA=t=>t==="family-name",rx=t=>t==="shadow";var EA=()=>{let t=Yt("color"),e=Yt("font"),n=Yt("text"),l=Yt("font-weight"),i=Yt("tracking"),r=Yt("leading"),a=Yt("breakpoint"),o=Yt("container"),u=Yt("spacing"),c=Yt("radius"),s=Yt("shadow"),f=Yt("inset-shadow"),p=Yt("text-shadow"),m=Yt("drop-shadow"),y=Yt("blur"),v=Yt("perspective"),E=Yt("aspect"),h=Yt("ease"),d=Yt("animate"),g=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],z=()=>[...S(),H,B],w=()=>["auto","hidden","clip","visible","scroll"],D=()=>["auto","contain","none"],T=()=>[H,B,u],q=()=>[Pi,"full","auto",...T()],k=()=>[fl,"none","subgrid",H,B],it=()=>["auto",{span:["full",fl,H,B]},fl,H,B],L=()=>[fl,"auto",H,B],_=()=>["auto","min","max","fr",H,B],j=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],G=()=>["start","end","center","stretch","center-safe","end-safe"],X=()=>["auto",...T()],lt=()=>[Pi,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],U=()=>[t,H,B],Kt=()=>[...S(),K0,F0,{position:[H,B]}],x=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Dt=()=>["auto","cover","contain",vA,yA,{size:[H,B]}],ce=()=>[lp,Ta,Ql],b=()=>["","none","full",c,H,B],rt=()=>["",W,Ta,Ql],Lt=()=>["solid","dashed","dotted","double"],Dn=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],gt=()=>[W,lp,K0,F0],bt=()=>["","none",y,H,B],Ze=()=>["none",W,H,B],se=()=>["none",W,H,B],qe=()=>[W,H,B],je=()=>[Pi,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Mn],breakpoint:[Mn],color:[mA],container:[Mn],"drop-shadow":[Mn],ease:["in","out","in-out"],font:[gA],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Mn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Mn],shadow:[Mn],spacing:["px",W],text:[Mn],"text-shadow":[Mn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Pi,B,H,E]}],container:["container"],columns:[{columns:[W,B,H,o]}],"break-after":[{"break-after":g()}],"break-before":[{"break-before":g()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:z()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[fl,"auto",H,B]}],basis:[{basis:[Pi,"full","auto",o,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[W,Pi,"auto","initial","none",B]}],grow:[{grow:["",W,H,B]}],shrink:[{shrink:["",W,H,B]}],order:[{order:[fl,"first","last","none",H,B]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:it()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:it()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...j(),"normal"]}],"justify-items":[{"justify-items":[...G(),"normal"]}],"justify-self":[{"justify-self":["auto",...G()]}],"align-content":[{content:["normal",...j()]}],"align-items":[{items:[...G(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...G(),{baseline:["","last"]}]}],"place-content":[{"place-content":j()}],"place-items":[{"place-items":[...G(),"baseline"]}],"place-self":[{"place-self":["auto",...G()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:X()}],mx:[{mx:X()}],my:[{my:X()}],ms:[{ms:X()}],me:[{me:X()}],mt:[{mt:X()}],mr:[{mr:X()}],mb:[{mb:X()}],ml:[{ml:X()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:lt()}],w:[{w:[o,"screen",...lt()]}],"min-w":[{"min-w":[o,"screen","none",...lt()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...lt()]}],h:[{h:["screen",...lt()]}],"min-h":[{"min-h":["screen","none",...lt()]}],"max-h":[{"max-h":["screen",...lt()]}],"font-size":[{text:["base",n,Ta,Ql]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,H,ip]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",lp,B]}],"font-family":[{font:[xA,B,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,H,B]}],"line-clamp":[{"line-clamp":[W,"none",H,ip]}],leading:[{leading:[r,...T()]}],"list-image":[{"list-image":["none",H,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",H,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Lt(),"wavy"]}],"text-decoration-thickness":[{decoration:[W,"from-font","auto",H,Ql]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[W,"auto",H,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Kt()}],"bg-repeat":[{bg:x()}],"bg-size":[{bg:Dt()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},fl,H,B],radial:["",H,B],conic:[fl,H,B]},SA,bA]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:ce()}],"gradient-via-pos":[{via:ce()}],"gradient-to-pos":[{to:ce()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:b()}],"rounded-s":[{"rounded-s":b()}],"rounded-e":[{"rounded-e":b()}],"rounded-t":[{"rounded-t":b()}],"rounded-r":[{"rounded-r":b()}],"rounded-b":[{"rounded-b":b()}],"rounded-l":[{"rounded-l":b()}],"rounded-ss":[{"rounded-ss":b()}],"rounded-se":[{"rounded-se":b()}],"rounded-ee":[{"rounded-ee":b()}],"rounded-es":[{"rounded-es":b()}],"rounded-tl":[{"rounded-tl":b()}],"rounded-tr":[{"rounded-tr":b()}],"rounded-br":[{"rounded-br":b()}],"rounded-bl":[{"rounded-bl":b()}],"border-w":[{border:rt()}],"border-w-x":[{"border-x":rt()}],"border-w-y":[{"border-y":rt()}],"border-w-s":[{"border-s":rt()}],"border-w-e":[{"border-e":rt()}],"border-w-t":[{"border-t":rt()}],"border-w-r":[{"border-r":rt()}],"border-w-b":[{"border-b":rt()}],"border-w-l":[{"border-l":rt()}],"divide-x":[{"divide-x":rt()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":rt()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Lt(),"hidden","none"]}],"divide-style":[{divide:[...Lt(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...Lt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[W,H,B]}],"outline-w":[{outline:["",W,Ta,Ql]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",s,ju,qu]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",f,ju,qu]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:rt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[W,Ql]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":rt()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",p,ju,qu]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[W,H,B]}],"mix-blend":[{"mix-blend":[...Dn(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Dn()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[W]}],"mask-image-linear-from-pos":[{"mask-linear-from":gt()}],"mask-image-linear-to-pos":[{"mask-linear-to":gt()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":gt()}],"mask-image-t-to-pos":[{"mask-t-to":gt()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":gt()}],"mask-image-r-to-pos":[{"mask-r-to":gt()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":gt()}],"mask-image-b-to-pos":[{"mask-b-to":gt()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":gt()}],"mask-image-l-to-pos":[{"mask-l-to":gt()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":gt()}],"mask-image-x-to-pos":[{"mask-x-to":gt()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":gt()}],"mask-image-y-to-pos":[{"mask-y-to":gt()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[H,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":gt()}],"mask-image-radial-to-pos":[{"mask-radial-to":gt()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[W]}],"mask-image-conic-from-pos":[{"mask-conic-from":gt()}],"mask-image-conic-to-pos":[{"mask-conic-to":gt()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Kt()}],"mask-repeat":[{mask:x()}],"mask-size":[{mask:Dt()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",H,B]}],filter:[{filter:["","none",H,B]}],blur:[{blur:bt()}],brightness:[{brightness:[W,H,B]}],contrast:[{contrast:[W,H,B]}],"drop-shadow":[{"drop-shadow":["","none",m,ju,qu]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",W,H,B]}],"hue-rotate":[{"hue-rotate":[W,H,B]}],invert:[{invert:["",W,H,B]}],saturate:[{saturate:[W,H,B]}],sepia:[{sepia:["",W,H,B]}],"backdrop-filter":[{"backdrop-filter":["","none",H,B]}],"backdrop-blur":[{"backdrop-blur":bt()}],"backdrop-brightness":[{"backdrop-brightness":[W,H,B]}],"backdrop-contrast":[{"backdrop-contrast":[W,H,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",W,H,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[W,H,B]}],"backdrop-invert":[{"backdrop-invert":["",W,H,B]}],"backdrop-opacity":[{"backdrop-opacity":[W,H,B]}],"backdrop-saturate":[{"backdrop-saturate":[W,H,B]}],"backdrop-sepia":[{"backdrop-sepia":["",W,H,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",H,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[W,"initial",H,B]}],ease:[{ease:["linear","initial",h,H,B]}],delay:[{delay:[W,H,B]}],animate:[{animate:["none",d,H,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,H,B]}],"perspective-origin":[{"perspective-origin":z()}],rotate:[{rotate:Ze()}],"rotate-x":[{"rotate-x":Ze()}],"rotate-y":[{"rotate-y":Ze()}],"rotate-z":[{"rotate-z":Ze()}],scale:[{scale:se()}],"scale-x":[{"scale-x":se()}],"scale-y":[{"scale-y":se()}],"scale-z":[{"scale-z":se()}],"scale-3d":["scale-3d"],skew:[{skew:qe()}],"skew-x":[{"skew-x":qe()}],"skew-y":[{"skew-y":qe()}],transform:[{transform:[H,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:z()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:je()}],"translate-x":[{"translate-x":je()}],"translate-y":[{"translate-y":je()}],"translate-z":[{"translate-z":je()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H,B]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[W,Ta,Ql,ip]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}};var ax=rA(EA);function ox(...t){return ax(Q0(t))}var cx=Ie(Yi());function ux(n){var l=n,{className:t}=l,e=Ca(l,["className"]);return(0,cx.jsx)("textarea",C({"data-slot":"textarea",className:ox("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t)},e))}var F=Ie(Yi()),wt={container:{position:"fixed",bottom:"20px",right:"20px",zIndex:2147483647,fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:"14px",lineHeight:"1.5",color:"#333",pointerEvents:"auto"},containerLeft:{left:"20px",right:"auto"},bubble:{width:"60px",height:"60px",borderRadius:"50%",border:"none",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",transition:"all 0.3s ease",outline:"none",position:"relative"},bubbleHover:{transform:"scale(1.05)",boxShadow:"0 6px 16px rgba(0, 0, 0, 0.2)"},chatWindow:{position:"absolute",bottom:"70px",right:"0",width:"360px",height:"600px",maxHeight:"calc(100vh - 100px)",backgroundColor:"#ffffff",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.12)",border:"1px solid #e5e7eb",display:"flex",flexDirection:"column",overflow:"hidden",transform:"translateY(10px)",opacity:0,transition:"all 0.3s ease",pointerEvents:"auto"},chatWindowLeft:{left:"0",right:"auto"},chatWindowOpen:{transform:"translateY(0)",opacity:1},header:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:"#ffffff"},headerTitle:{margin:0,fontSize:"16px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"600",color:"#111827"},closeButton:{background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px",color:"#6b7280",fontSize:"18px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',lineHeight:1,outline:"none"},messagesContainer:{flex:1,padding:"16px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px"},message:{maxWidth:"80%",padding:"8px 12px",borderRadius:"12px",fontSize:"14px",lineHeight:"1.4"},userMessage:{alignSelf:"flex-end",backgroundColor:"#3b82f6",color:"#ffffff"},assistantMessage:{alignSelf:"flex-start",backgroundColor:"#f3f4f6",color:"#111827"},inputContainer:{padding:"16px",borderTop:"1px solid #e5e7eb",display:"flex",gap:"8px",backgroundColor:"#ffffff",color:"#111827 !important"},input:{flex:1,padding:"8px 12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',outline:"none",resize:"none",minHeight:"36px",maxHeight:"100px",backgroundColor:"#ffffff"},sendButton:{padding:"8px 16px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"500",outline:"none",transition:"all 0.2s ease"},sendButtonDisabled:{opacity:.5,cursor:"not-allowed"},brandingFooter:{padding:"8px 16px",textAlign:"center",borderTop:"1px solid #f3f4f6",backgroundColor:"#fafafa"},brandingText:{fontSize:"11px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',color:"#9ca3af",margin:0},brandingLink:{color:"#6b7280",textDecoration:"none",fontWeight:"500",transition:"color 0.2s ease"},markdownContent:{lineHeight:"1.6"},markdownH1:{fontSize:"18px",fontWeight:"700",margin:"16px 0 8px 0",color:"inherit"},markdownH2:{fontSize:"16px",fontWeight:"600",margin:"14px 0 6px 0",color:"inherit"},markdownH3:{fontSize:"15px",fontWeight:"600",margin:"12px 0 4px 0",color:"inherit"},markdownP:{margin:"8px 0",color:"inherit"},markdownUl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownOl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownLi:{margin:"4px 0",color:"inherit"},markdownCode:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownPre:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownBlockquote:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},markdownA:{color:"#3b82f6",textDecoration:"underline"},markdownStrong:{fontWeight:"600",color:"inherit"},markdownEm:{fontStyle:"italic",color:"inherit"}};function TA({content:t,primaryColor:e}){return(0,F.jsx)("div",{style:wt.markdownContent,children:(0,F.jsx)(xm,{remarkPlugins:[Hu],components:{h1:({children:n})=>(0,F.jsx)("h1",{style:{fontSize:"18px",fontWeight:700,margin:"16px 0 8px 0",color:"inherit"},children:n}),h2:({children:n})=>(0,F.jsx)("h2",{style:{fontSize:"16px",fontWeight:600,margin:"14px 0 6px 0",color:"inherit"},children:n}),h3:({children:n})=>(0,F.jsx)("h3",{style:{fontSize:"15px",fontWeight:600,margin:"12px 0 4px 0",color:"inherit"},children:n}),p:({children:n})=>(0,F.jsx)("p",{style:{margin:"8px 0",color:"inherit"},children:n}),ul:({children:n})=>(0,F.jsx)("ul",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),ol:({children:n})=>(0,F.jsx)("ol",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),li:({children:n})=>(0,F.jsx)("li",{style:{margin:"4px 0",color:"inherit"},children:n}),code:({children:n,className:l})=>l?(0,F.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit",display:"block"},children:n}):(0,F.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),pre:({children:n})=>(0,F.jsx)("pre",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),blockquote:({children:n})=>(0,F.jsx)("blockquote",{style:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},children:n}),a:({children:n,href:l})=>(0,F.jsx)("a",{href:l,style:{color:e,textDecoration:"underline"},target:"_blank",rel:"noopener noreferrer",children:n}),strong:({children:n})=>(0,F.jsx)("strong",{style:{fontWeight:600,color:"inherit"},children:n}),em:({children:n})=>(0,F.jsx)("em",{style:{fontStyle:"italic",color:"inherit"},children:n})},children:t})})}function AA(){return`
		/* Mobile responsive styles */
		@media (max-width: 640px) {
			.chat-widget-container {
				position: fixed !important;
				bottom: 10px !important;
				left: 10px !important;
				right: 10px !important;
				width: auto !important;
			}

			.chat-widget-bubble {
				width: 56px !important;
				height: 56px !important;
				position: fixed !important;
				bottom: 20px !important;
				right: 20px !important;
			}

			.chat-widget-window {
				position: fixed !important;
				bottom: 0 !important;
				left: 0 !important;
				right: 0 !important;
				top: auto !important;
				width: 100vw !important;
				height: 100vh !important;
				max-height: 100vh !important;
				border-radius: 0 !important;
				border: none !important;
				box-shadow: none !important;
			}

			.chat-widget-header {
				padding: 16px 20px !important;
				min-height: 60px !important;
			}

			.chat-widget-close-button {
				padding: 8px !important;
				font-size: 20px !important;
				min-width: 44px !important;
				min-height: 44px !important;
			}

			.chat-widget-messages {
				padding: 12px 16px !important;
			}

			.chat-widget-message {
				max-width: 85% !important;
				font-size: 16px !important;
				padding: 12px 16px !important;
			}

			.chat-widget-input-container {
				padding: 16px !important;
				gap: 12px !important;
			}

			.chat-widget-input {
				font-size: 16px !important;
				padding: 12px 16px !important;
				min-height: 44px !important;
			}

			.chat-widget-send-button {
				padding: 12px 20px !important;
				font-size: 16px !important;
				min-height: 44px !important;
				min-width: 80px !important;
			}

			.chat-widget-branding {
				padding: 12px 16px !important;
			}
		}

		/* Small mobile devices (320px and up) */
		@media (max-width: 375px) {
			.chat-widget-input-container {
				flex-direction: column !important;
				gap: 8px !important;
			}

			.chat-widget-send-button {
				width: 100% !important;
			}

			.chat-widget-message {
				max-width: 90% !important;
				font-size: 15px !important;
			}
		}

		/* Tablet landscape and small desktop */
		@media (min-width: 641px) and (max-width: 1024px) {
			.chat-widget-window {
				width: 380px !important;
				height: 650px !important;
			}
		}
	`}function zA(t){let e=(a,o)=>{let u=a.replace("#",""),c,s,f;return u.length===3?(c=Number.parseInt(u[0]+u[0],16),s=Number.parseInt(u[1]+u[1],16),f=Number.parseInt(u[2]+u[2],16)):(c=Number.parseInt(u.slice(0,2),16),s=Number.parseInt(u.slice(2,4),16),f=Number.parseInt(u.slice(4,6),16)),`rgba(${c}, ${s}, ${f}, ${o})`},n=e(t,.08),l=e(t,.25),i=e(t,.4),r=e(t,.6);return`
		/* WebKit browsers (Chrome, Safari, Edge) */
		.chat-messages-container::-webkit-scrollbar {
			width: 6px;
			background: transparent;
		}

		.chat-messages-container::-webkit-scrollbar-track {
			background: ${n};
			border-radius: 3px;
			margin: 2px 0;
		}

		.chat-messages-container::-webkit-scrollbar-thumb {
			background: ${l};
			border-radius: 3px;
			transition: all 0.2s ease;
			min-height: 20px;
		}

		.chat-messages-container::-webkit-scrollbar-thumb:hover {
			background: ${i};
		}

		.chat-messages-container::-webkit-scrollbar-thumb:active {
			background: ${r};
		}

		/* Hide scrollbar when not needed */
		.chat-messages-container::-webkit-scrollbar-corner {
			background: transparent;
		}

		/* Firefox */
		.chat-messages-container {
			scrollbar-width: thin;
			scrollbar-color: ${l} ${n};
		}

		/* Ensure smooth scrolling and proper behavior */
		.chat-messages-container {
			scroll-behavior: smooth;
			overflow-x: hidden;
			overflow-y: auto;
		}

		/* Hide scrollbar on mobile devices where it's not needed */
		@media (max-width: 640px) {
			.chat-messages-container::-webkit-scrollbar {
				width: 4px;
			}
		}

		/* Ensure scrollbar doesn't interfere with content */
		.chat-messages-container {
			scrollbar-gutter: stable;
		}

		/* Add subtle fade effect for better visual integration */
		.chat-messages-container::-webkit-scrollbar-thumb {
			box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
		}

		/* Improve accessibility - ensure scrollbar is visible for keyboard navigation */
		.chat-messages-container:focus-within::-webkit-scrollbar-thumb {
			background: ${i};
		}
	`}function CA({config:t,shadowRoot:e}){console.log("[Widget] Initializing with config:",{websiteId:t.websiteId,visitorId:t.visitorId,conversationId:t.conversationId,baseUrl:t.baseUrl,hasOnConversationIdChange:!!t.onConversationIdChange});let[n,l]=(0,Ft.useState)(t.initiallyOpen||!1),[i,r]=(0,Ft.useState)([]),[a,o]=(0,Ft.useState)(""),[u,c]=(0,Ft.useState)(!1),[s,f]=(0,Ft.useState)(!1),[p,m]=(0,Ft.useState)(!1),[y,v]=(0,Ft.useState)(t.conversationId||null),E=(0,Ft.useRef)(null),h=t.primaryColor||"#3b82f6",d=t.secondaryColor||"#ffffff",g=t.position||"bottom-right",S=L=>{let _=L.replace("#",""),j=Number.parseInt(_.substring(0,2),16),G=Number.parseInt(_.substring(2,4),16),X=Number.parseInt(_.substring(4,6),16);return(j*299+G*587+X*114)/1e3>128?"#111827":"#ffffff"},z=S(d),w=S("#f3f4f6");(0,Ft.useEffect)(()=>{if(e){let L=e.querySelector("#chat-scrollbar-styles"),_=e.querySelector("#chat-mobile-styles");L&&L.remove(),_&&_.remove();let j=document.createElement("style");j.id="chat-scrollbar-styles",j.textContent=zA(h),e.appendChild(j);let G=document.createElement("style");G.id="chat-mobile-styles",G.textContent=AA(),e.appendChild(G)}},[e,h]);let D=(0,Ft.useCallback)(()=>Il(this,null,function*(){if(!t.visitorId||!t.websiteId||p){console.log(`[Widget] Skipping history load - visitorId: ${t.visitorId}, websiteId: ${t.websiteId}, isLoadingHistory: ${p}`);return}m(!0),console.log(`[Widget] Loading chat history for visitor: ${t.visitorId}, website: ${t.websiteId}`);try{let L=`${t.baseUrl}/api/chat/history/visitor/${t.websiteId}/${t.visitorId}`;console.log(`[Widget] Fetching history from: ${L}`);let _=yield fetch(L,{method:"GET",headers:{"Content-Type":"application/json"}});if(console.log(`[Widget] History API response status: ${_.status}`),!_.ok){console.warn("[Widget] Failed to load chat history:",_.status);return}let j=yield _.json();if(console.log("[Widget] Loaded chat history data:",j),j.conversation&&j.messages&&j.messages.length>0){v(j.conversation.id),t.onConversationIdChange&&t.onConversationIdChange(j.conversation.id);let G=j.messages.map(X=>({id:X.id,content:X.content,role:X.role,timestamp:new Date(X.timestamp)}));r(G),console.log(`[Widget] Loaded ${G.length} messages from history`)}else console.log("[Widget] No previous conversation found")}catch(L){console.error("[Widget] Error loading chat history:",L)}finally{m(!1)}}),[t.visitorId,t.websiteId,t.baseUrl,t.onConversationIdChange,p]);(0,Ft.useEffect)(()=>{E.current&&E.current.scrollIntoView({behavior:"smooth"})},[i]),(0,Ft.useEffect)(()=>{n&&i.length===0&&t.visitorId&&!p&&D()},[n,i.length,t.visitorId,p,D]),(0,Ft.useEffect)(()=>{n&&i.length===0&&t.welcomeMessage&&!p&&r([{id:"welcome",content:t.welcomeMessage,role:"assistant",timestamp:new Date}])},[n,i.length,t.welcomeMessage,p]);let T=(0,Ft.useCallback)(()=>Il(this,null,function*(){var G;if(!a.trim()||u)return;let L={id:Date.now().toString(),content:a.trim(),role:"user",timestamp:new Date};r(X=>[...X,L]),o(""),c(!0);let _=(Date.now()+1).toString(),j={id:_,content:"",role:"assistant",timestamp:new Date};r(X=>[...X,j]);try{let X=yield fetch(`${t.baseUrl}/api/chat/${t.websiteId}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...i,L].map(x=>({role:x.role,content:x.content})),websiteId:t.websiteId,visitorId:t.visitorId,conversationId:y})});if(!X.ok)throw new Error("Failed to send message");let lt=(G=X.body)==null?void 0:G.getReader(),U=new TextDecoder,Kt="";if(lt)for(;;){let{done:x,value:Dt}=yield lt.read();if(x)break;let b=U.decode(Dt,{stream:!0}).split(`
`);for(let rt of b)if(rt.startsWith("c:")){let Lt=rt.slice(2).trim();console.log(`[Widget] Received conversation ID from stream: ${Lt}`),v(Lt),t.onConversationIdChange&&(console.log(`[Widget] Storing conversation ID in localStorage: ${Lt}`),t.onConversationIdChange(Lt))}else if(rt.startsWith("0:")){let Lt=rt.slice(2);Kt+=Lt,u&&Lt.trim()&&c(!1);let Dn=V0(Kt);r(gt=>gt.map(bt=>bt.id===_?Et(C({},bt),{content:Dn}):bt))}}Kt||r(x=>x.map(Dt=>Dt.id===_?Et(C({},Dt),{content:"Sorry, I encountered an error."}):Dt))}catch(X){console.error("Error sending message:",X),r(lt=>lt.map(U=>U.id===_?Et(C({},U),{content:"Sorry, I encountered an error. Please try again."}):U))}finally{c(!1)}}),[a,u,i,t,y]),q=L=>{L.key==="Enter"&&!L.shiftKey&&(L.preventDefault(),T())},k=()=>{l(!n)},it=()=>{l(!1)};return(0,F.jsxs)("div",{className:"chat-widget-container",style:C(C({},wt.container),g==="bottom-left"?wt.containerLeft:{}),children:[n&&(0,F.jsxs)("div",{className:"chat-widget-window",style:C(C(C({},wt.chatWindow),g==="bottom-left"?wt.chatWindowLeft:{}),wt.chatWindowOpen),children:[(0,F.jsxs)("div",{className:"chat-widget-header",style:Et(C({},wt.header),{backgroundColor:d}),children:[(0,F.jsx)("h3",{style:Et(C({},wt.headerTitle),{color:z}),children:t.headerText||"Chat Assistant"}),(0,F.jsx)("button",{type:"button",className:"chat-widget-close-button",style:Et(C({},wt.closeButton),{color:z}),onClick:it,"aria-label":"Close chat",children:"\xD7"})]}),(0,F.jsxs)("div",{className:"chat-messages-container chat-widget-messages",style:wt.messagesContainer,children:[p&&(0,F.jsx)("div",{style:Et(C(C({},wt.message),wt.assistantMessage),{color:w,fontStyle:"italic"}),children:"Loading previous conversation..."}),i.map(L=>(0,F.jsx)("div",{className:"chat-widget-message",style:C(C(C({},wt.message),L.role==="user"?wt.userMessage:Et(C({},wt.assistantMessage),{color:w})),L.role==="user"?{backgroundColor:h}:{}),children:L.role==="assistant"?L.content.trim()===""&&u?(0,F.jsx)("div",{style:{color:w,fontStyle:"italic"},children:"Thinking..."}):(0,F.jsx)(TA,{content:L.content,primaryColor:h}):L.content},L.id)),(0,F.jsx)("div",{ref:E})]}),(0,F.jsxs)("div",{className:"chat-widget-input-container",style:Et(C({},wt.inputContainer),{backgroundColor:d,color:z}),children:[(0,F.jsx)(ux,{className:"chat-widget-input",style:Et(C({},wt.input),{backgroundColor:d,color:z}),value:a,onChange:L=>o(L.target.value),onKeyDown:q,placeholder:"Type your message...",rows:1}),(0,F.jsx)("button",{type:"button",className:"chat-widget-send-button",style:C(Et(C({},wt.sendButton),{backgroundColor:h,color:"#ffffff"}),!a.trim()||u?wt.sendButtonDisabled:{}),onClick:T,disabled:!a.trim()||u,children:"Send"})]}),(0,F.jsx)("div",{className:"chat-widget-branding",style:wt.brandingFooter,children:(0,F.jsxs)("p",{style:wt.brandingText,children:["Powered by"," ",(0,F.jsx)("a",{href:"https://bublai.com",target:"_blank",rel:"noopener noreferrer",style:wt.brandingLink,"aria-label":"Visit Bubl website",onMouseEnter:L=>{L.currentTarget.style.color=h},onMouseLeave:L=>{L.currentTarget.style.color="#6b7280"},children:"Bubl"})]})})]}),(0,F.jsx)("button",{type:"button",className:"chat-widget-bubble",style:C(Et(C({},wt.bubble),{backgroundColor:h}),s?wt.bubbleHover:{}),onClick:k,onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),"aria-label":n?"Close chat":"Open chat",children:(0,F.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",style:{color:"#ffffff"},children:[(0,F.jsx)("title",{children:n?"Close chat":"Open chat"}),n?(0,F.jsx)("path",{d:"M18 6L6 18M6 6l12 12"}):(0,F.jsx)("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})]})})]})}function fx({shadowRoot:t,config:e}){let n=document.createElement("div");t.appendChild(n);let l=(0,sx.createRoot)(n);return l.render((0,F.jsx)(CA,{config:e,shadowRoot:t})),{open(){},close(){},toggle(){},destroy(){l.unmount(),n.parentNode&&n.parentNode.removeChild(n)}}}window.BublWidgetV2={init:fx};})();
/*! Bundled license information:

react/cjs/react.production.js:
  (**
   * @license React
   * react.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.js:
  (**
   * @license React
   * scheduler.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.js:
  (**
   * @license React
   * react-dom.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom-client.production.js:
  (**
   * @license React
   * react-dom-client.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.js:
  (**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
