/**
 * Bubl Widget Loader v2.0
 * Complete rewrite with Shadow DOM isolation and zero host interference
 */
(function() {
  'use strict';
  
  // Prevent multiple widget instances
  if (window.__BUBL_WIDGET_LOADED__) {
    console.warn('Bubl widget already loaded');
    return;
  }
  window.__BUBL_WIDGET_LOADED__ = true;

  // Configuration validation
  const config = window.Bubl?.config;
  if (!config?.websiteId) {
    console.error('Bubl: Missing websiteId in configuration');
    return;
  }

  // Get base URL from script source
  const currentScript = document.currentScript || 
    Array.from(document.scripts).find(s => s.src.includes('/widget/v2/loader.js'));
  
  if (!currentScript) {
    console.error('Bubl: Could not determine widget base URL');
    return;
  }

  const baseUrl = currentScript.src.replace('/widget/v2/loader.js', '');
  
  // Widget state management
  let widgetInstance = null;
  let shadowRoot = null;
  let isInitialized = false;

  // Utility functions
  function generateId() {
    return 'bubl-' + Math.random().toString(36).substr(2, 9);
  }

  function getStoredVisitorId() {
    try {
      const key = `bubl_visitor_${config.websiteId}`;
      const timestampKey = `bubl_visitor_timestamp_${config.websiteId}`;
      const expirationDays = 30; // Visitor ID expires after 30 days

      console.log(`[Loader] Getting visitor ID for website: ${config.websiteId}`);

      let visitorId = localStorage.getItem(key);
      const timestamp = localStorage.getItem(timestampKey);

      console.log(`[Loader] Found stored visitor ID: ${visitorId}, timestamp: ${timestamp}`);

      // Check if visitor ID exists and is not expired
      if (visitorId && timestamp) {
        const now = Date.now();
        const storedTime = parseInt(timestamp, 10);
        const daysSinceStored = (now - storedTime) / (1000 * 60 * 60 * 24);

        console.log(`[Loader] Visitor ID age: ${daysSinceStored.toFixed(2)} days`);

        if (daysSinceStored < expirationDays) {
          console.log(`[Loader] Using existing visitor ID: ${visitorId}`);
          return visitorId;
        }

        // Clean up expired visitor ID
        console.log(`[Loader] Visitor ID expired, cleaning up`);
        localStorage.removeItem(key);
        localStorage.removeItem(timestampKey);
        localStorage.removeItem(`bubl_conversation_${config.websiteId}`);
      }

      // Generate new visitor ID
      visitorId = generateId();
      localStorage.setItem(key, visitorId);
      localStorage.setItem(timestampKey, Date.now().toString());

      console.log(`[Loader] Generated new visitor ID: ${visitorId}`);
      return visitorId;
    } catch (e) {
      console.error(`[Loader] Error with localStorage, using fallback visitor ID:`, e);
      return generateId();
    }
  }

  function getStoredConversationId() {
    try {
      const key = `bubl_conversation_${config.websiteId}`;
      const conversationId = localStorage.getItem(key);
      console.log(`[Loader] Getting conversation ID for website ${config.websiteId}: ${conversationId}`);
      return conversationId;
    } catch (e) {
      console.error(`[Loader] Error getting conversation ID:`, e);
      return null;
    }
  }

  function setStoredConversationId(conversationId) {
    try {
      const key = `bubl_conversation_${config.websiteId}`;
      console.log(`[Loader] Storing conversation ID for website ${config.websiteId}: ${conversationId}`);
      localStorage.setItem(key, conversationId);
      console.log(`[Loader] Successfully stored conversation ID`);
    } catch (e) {
      console.error(`[Loader] Error storing conversation ID:`, e);
    }
  }

  // Performance tracking (minimal and non-blocking)
  function trackEvent(eventType, data = {}) {
    if (!config.websiteId) return;
    
    // Use requestIdleCallback for non-critical tracking
    const track = () => {
      fetch(`${baseUrl}/api/analytics/track-event`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          websiteId: config.websiteId,
          visitorId: getStoredVisitorId(),
          conversationId: generateId(),
          eventType,
          metadata: { ...data, source: 'widget_v2' }
        })
      }).catch(() => {}); // Silent fail for analytics
    };

    if (window.requestIdleCallback) {
      requestIdleCallback(track);
    } else {
      setTimeout(track, 0);
    }
  }

  // Create isolated widget container with Shadow DOM
  function createWidgetContainer() {
    const container = document.createElement('div');
    container.id = 'bubl-widget-v2';
    
    // Position the container
    container.style.cssText = `
      position: fixed !important;
      z-index: 2147483647 !important;
      pointer-events: none !important;
      ${config.position === 'bottom-left' ? 'left: 20px !important;' : 'right: 20px !important;'}
      bottom: 20px !important;
      width: 0 !important;
      height: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      background: transparent !important;
      overflow: visible !important;
    `;

    // Create Shadow DOM for complete isolation
    if (container.attachShadow) {
      shadowRoot = container.attachShadow({ mode: 'closed' });
    } else {
      // Fallback for older browsers
      shadowRoot = container;
      console.warn('Bubl: Shadow DOM not supported, using fallback');
    }

    document.body.appendChild(container);
    return container;
  }

  // Load widget React bundle
  function loadWidgetBundle() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.async = true;
      script.onload = resolve;
      script.onerror = reject;
      script.src = `${baseUrl}/widget/v2/bundle.js`;
      
      // Append to shadow root or document head
      if (shadowRoot.appendChild) {
        shadowRoot.appendChild(script);
      } else {
        document.head.appendChild(script);
      }
    });
  }

  // Initialize widget
  async function initializeWidget() {
    if (isInitialized) return;
    isInitialized = true;

    try {
      const startTime = performance.now();
      
      // Create container
      const container = createWidgetContainer();
      
      // Load widget bundle
      await loadWidgetBundle();
      
      // Initialize React widget
      if (window.BublWidgetV2) {
        const visitorId = getStoredVisitorId();
        const conversationId = getStoredConversationId();

        console.log(`[Loader] Initializing widget with:`, {
          websiteId: config.websiteId,
          visitorId,
          conversationId,
          baseUrl
        });

        widgetInstance = window.BublWidgetV2.init({
          shadowRoot,
          config: {
            ...config,
            baseUrl,
            visitorId,
            conversationId,
            onConversationIdChange: setStoredConversationId
          }
        });

        // Track successful initialization
        const loadTime = performance.now() - startTime;
        trackEvent('widget_v2_loaded', { loadTime });

        // Call ready callback
        if (typeof config.onReady === 'function') {
          config.onReady();
        }
      } else {
        throw new Error('Widget bundle failed to load');
      }
    } catch (error) {
      console.error('Bubl widget initialization failed:', error);
      trackEvent('widget_v2_error', { error: error.message });
    }
  }

  // Public API
  window.Bubl = window.Bubl || {};
  window.Bubl.api = {
    open() {
      if (widgetInstance?.open) widgetInstance.open();
    },
    close() {
      if (widgetInstance?.close) widgetInstance.close();
    },
    toggle() {
      if (widgetInstance?.toggle) widgetInstance.toggle();
    },
    destroy() {
      if (widgetInstance?.destroy) widgetInstance.destroy();
      const container = document.getElementById('bubl-widget-v2');
      if (container) container.remove();
      isInitialized = false;
      widgetInstance = null;
      shadowRoot = null;
    }
  };

  // Initialize widget when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidget);
  } else {
    // DOM is already ready
    setTimeout(initializeWidget, 0);
  }

})();
