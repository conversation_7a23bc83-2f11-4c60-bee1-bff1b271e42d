# Chat Persistence Implementation

This document describes the chat persistence functionality that maintains conversation continuity across page navigation for the Bubl chat widget.

## Overview

The chat persistence feature allows visitors to maintain their conversation context when navigating between pages on a website. This is achieved through:

1. **Visitor ID Management**: Unique visitor identification with localStorage persistence
2. **Conversation Tracking**: Database-backed conversation storage linked to visitor IDs
3. **History Loading**: Automatic retrieval of previous conversations when the widget opens
4. **Seamless Continuity**: Messages persist across browser sessions and page navigation

## Implementation Details

### 1. Visitor ID Management

**Location**: `public/widget/v2/loader.js`

- Generates unique visitor IDs using the format: `bubl-{random}`
- Stores visitor ID in localStorage with key: `bubl_visitor_{websiteId}`
- Implements 30-day expiration for visitor IDs
- Includes fallback for localStorage unavailability

```javascript
function getStoredVisitorId() {
  // Generates or retrieves visitor ID with 30-day expiration
  // Handles localStorage errors gracefully
}
```

### 2. Conversation ID Persistence

**Location**: `public/widget/v2/loader.js`

- Stores active conversation ID in localStorage with key: `bubl_conversation_{websiteId}`
- Automatically cleans up expired conversation IDs when visitor ID expires
- Provides callback mechanism for conversation ID updates

```javascript
function getStoredConversationId() {
  // Retrieves stored conversation ID
}

function setStoredConversationId(conversationId) {
  // Stores conversation ID for persistence
}
```

### 3. Database Schema

**Existing Schema**: Already supports visitor-based conversations

- `conversations.visitor_id`: Links conversations to specific visitors
- `conversations.status`: Tracks active/inactive conversations
- `messages.conversation_id`: Links messages to conversations

### 4. API Endpoints

#### New Endpoint: `/api/chat/history/visitor/[websiteId]/[visitorId]`

**Purpose**: Retrieves conversation history for a specific visitor on a website

**Method**: GET

**Response**:
```json
{
  "conversation": {
    "id": "uuid",
    "websiteId": "uuid",
    "visitorId": "string",
    "status": "active",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  },
  "messages": [
    {
      "id": "uuid",
      "content": "string",
      "role": "user|assistant",
      "timestamp": "timestamp"
    }
  ]
}
```

**CORS Support**: Includes proper CORS headers for cross-origin requests

### 5. Widget Implementation

**Location**: `src/components/widget-v2/WidgetV2.tsx`

#### New State Variables:
- `isLoadingHistory`: Tracks history loading state
- `currentConversationId`: Stores active conversation ID

#### New Configuration Options:
- `conversationId`: Initial conversation ID from localStorage
- `onConversationIdChange`: Callback for conversation ID updates

#### History Loading Process:
1. Triggers when widget opens and no messages exist
2. Fetches conversation history using visitor ID
3. Loads previous messages into the widget
4. Updates conversation ID for future messages
5. Shows loading indicator during fetch

#### Enhanced Message Sending:
- Includes conversation ID in API requests
- Maintains conversation continuity across messages
- Updates localStorage with new conversation IDs

## User Experience

### First-Time Visitors
1. Widget opens with welcome message (if configured)
2. New visitor ID generated and stored
3. New conversation created on first message
4. Conversation ID stored for future sessions

### Returning Visitors
1. Widget opens with loading indicator
2. Previous conversation history loaded automatically
3. Chat continues from where it left off
4. New messages append to existing conversation

### Cross-Page Navigation
1. Visitor ID and conversation ID persist in localStorage
2. Widget maintains conversation context on new pages
3. No interruption to ongoing conversations
4. Seamless user experience across the website

## Error Handling

### localStorage Unavailability
- Graceful fallback to session-based IDs
- Widget continues to function without persistence
- No errors thrown to the user

### API Failures
- Silent failure for history loading
- Widget starts fresh if history unavailable
- Error logging for debugging purposes

### Network Issues
- Timeout handling for history requests
- Retry mechanisms not implemented (by design for performance)
- Graceful degradation to new conversation

## Performance Considerations

### Lazy Loading
- History only loaded when widget opens
- No background requests or preloading
- Minimal impact on page load performance

### Caching
- Visitor IDs cached for 30 days
- Conversation IDs persist across sessions
- No additional API calls for known visitors

### Memory Management
- History loaded once per session
- No continuous polling or updates
- Efficient message storage in component state

## Security Considerations

### Data Privacy
- Visitor IDs are randomly generated (not personally identifiable)
- No sensitive data stored in localStorage
- Conversation data secured server-side

### CORS Protection
- Proper CORS headers on API endpoints
- Domain-specific visitor ID storage
- No cross-domain data leakage

### Input Validation
- Visitor ID format validation
- Conversation ID UUID validation
- Sanitized message content

## Testing Recommendations

### Manual Testing
1. Open widget on a page and start a conversation
2. Navigate to another page on the same website
3. Open widget again - conversation should continue
4. Close browser and return - conversation should persist
5. Wait 30+ days - visitor ID should reset

### Automated Testing
1. Test visitor ID generation and storage
2. Test conversation history API endpoint
3. Test localStorage fallback mechanisms
4. Test conversation continuity across page loads
5. Test expiration and cleanup functionality

## Future Enhancements

### Potential Improvements
1. **Conversation Expiration**: Automatic cleanup of old conversations
2. **Multiple Conversations**: Support for multiple active conversations per visitor
3. **Conversation Resumption**: Smart detection of conversation context changes
4. **Offline Support**: Queue messages when offline, sync when online
5. **Analytics Integration**: Track conversation persistence metrics

### Configuration Options
1. **Persistence Duration**: Configurable visitor ID expiration
2. **History Limits**: Maximum messages to load from history
3. **Auto-cleanup**: Automatic removal of old conversations
4. **Privacy Mode**: Option to disable persistence entirely
