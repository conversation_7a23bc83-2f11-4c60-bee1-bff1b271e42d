import { test, expect } from '@playwright/test';
import { cleanStreamingContent } from '../src/lib/utils/content-cleaner';

test.describe('Content Cleaner Utility', () => {
  test('should clean basic quote artifacts', () => {
    const input = '"Hello""! How"" can I assist you"" today?"';
    const expected = 'Hello! How can I assist you today?';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle complex quote patterns', () => {
    const input = '"Hello" "! How" " can I assist you" " today? If" " you have any questions" " or need help" " with something specific," " feel free to let" " me know!"';
    const expected = 'Hello! How can I assist you today? If you have any questions or need help with something specific, feel free to let me know!';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should convert literal newlines', () => {
    const input = 'Hello\\nWorld\\r\\nTest\\r';
    const expected = 'Hello\nWorld\nTest\n';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle apostrophes correctly', () => {
    const input = '"I""\'""m"" here to help"';
    const expected = "I'm here to help";
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should remove surrounding quotes', () => {
    const input = '"This is a complete message"';
    const expected = 'This is a complete message';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle multiple quote types', () => {
    const input = '"""Hello world"""';
    const expected = 'Hello world';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle escaped quotes', () => {
    const input = '\\"Hello\\" world';
    const expected = '"Hello" world';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle empty or null input', () => {
    expect(cleanStreamingContent('')).toBe('');
    expect(cleanStreamingContent(null as any)).toBe('');
    expect(cleanStreamingContent(undefined as any)).toBe('');
  });

  test('should preserve legitimate quotes in content', () => {
    const input = 'He said "Hello" to me';
    const expected = 'He said "Hello" to me';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle JSON stringification artifacts', () => {
    const input = '"Hello " "world"';
    const expected = 'Hello world';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle real-world streaming example', () => {
    // This is the exact pattern mentioned in the issue
    const input = '"Hello" "! How" " can I assist you" " today? If" " you have any questions" " or need help" " with something specific," " feel free to let" " me know!"';
    const expected = 'Hello! How can I assist you today? If you have any questions or need help with something specific, feel free to let me know!';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle multiline content', () => {
    const input = '"Hello\\nThis is a\\nmultiline message"';
    const expected = 'Hello\nThis is a\nmultiline message';
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });

  test('should handle mixed quote and apostrophe patterns', () => {
    const input = '"I""\'""ll"" help you with that"';
    const expected = "I'll help you with that";
    const result = cleanStreamingContent(input);
    expect(result).toBe(expected);
  });
});

// Integration test for the API endpoint
test.describe('Chat API Content Storage', () => {
  test('should store cleaned content in database', async ({ request }) => {
    // This test would require a test database setup
    // For now, we'll just test that the API endpoint exists and responds
    const response = await request.options('/api/chat/test-website-id');
    expect(response.status()).toBe(200);
  });

  test('should include conversation ID in streaming response', async ({ request }) => {
    // Test that the conversation ID is properly included in the stream
    // This would require setting up a test conversation
    const response = await request.options('/api/chat/test-website-id');
    expect(response.status()).toBe(200);
  });
});
