import { test, expect } from '@playwright/test';

test.describe('Chat Widget Persistence', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a test page with the widget
    await page.goto('/');
  });

  test('should persist visitor ID across page reloads', async ({ page }) => {
    // Mock localStorage to test visitor ID persistence
    await page.addInitScript(() => {
      // Mock a website ID for testing
      window.testWebsiteId = 'test-website-123';
    });

    // Evaluate visitor ID generation logic
    const visitorId1 = await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      const key = `bubl_visitor_${websiteId}`;
      
      // Simulate the visitor ID generation logic
      function generateId() {
        return 'bubl-' + Math.random().toString(36).substr(2, 9);
      }
      
      let visitorId = localStorage.getItem(key);
      if (!visitorId) {
        visitorId = generateId();
        localStorage.setItem(key, visitorId);
        localStorage.setItem(`bubl_visitor_timestamp_${websiteId}`, Date.now().toString());
      }
      
      return visitorId;
    });

    // Reload the page
    await page.reload();

    // Check that the same visitor ID is retrieved
    const visitorId2 = await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      const key = `bubl_visitor_${websiteId}`;
      return localStorage.getItem(key);
    });

    expect(visitorId1).toBe(visitorId2);
    expect(visitorId1).toMatch(/^bubl-[a-z0-9]{9}$/);
  });

  test('should clean up expired visitor ID', async ({ page }) => {
    await page.addInitScript(() => {
      window.testWebsiteId = 'test-website-456';
    });

    // Set an expired visitor ID
    await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      const expiredTimestamp = Date.now() - (31 * 24 * 60 * 60 * 1000); // 31 days ago
      
      localStorage.setItem(`bubl_visitor_${websiteId}`, 'expired-visitor-id');
      localStorage.setItem(`bubl_visitor_timestamp_${websiteId}`, expiredTimestamp.toString());
      localStorage.setItem(`bubl_conversation_${websiteId}`, 'expired-conversation-id');
    });

    // Simulate visitor ID retrieval with expiration check
    const newVisitorId = await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      const key = `bubl_visitor_${websiteId}`;
      const timestampKey = `bubl_visitor_timestamp_${websiteId}`;
      const expirationDays = 30;
      
      function generateId() {
        return 'bubl-' + Math.random().toString(36).substr(2, 9);
      }
      
      let visitorId = localStorage.getItem(key);
      const timestamp = localStorage.getItem(timestampKey);
      
      if (visitorId && timestamp) {
        const now = Date.now();
        const storedTime = parseInt(timestamp, 10);
        const daysSinceStored = (now - storedTime) / (1000 * 60 * 60 * 24);
        
        if (daysSinceStored >= expirationDays) {
          // Clean up expired data
          localStorage.removeItem(key);
          localStorage.removeItem(timestampKey);
          localStorage.removeItem(`bubl_conversation_${websiteId}`);
          visitorId = null;
        }
      }
      
      if (!visitorId) {
        visitorId = generateId();
        localStorage.setItem(key, visitorId);
        localStorage.setItem(timestampKey, Date.now().toString());
      }
      
      return visitorId;
    });

    // Verify that expired data was cleaned up and new ID was generated
    expect(newVisitorId).toMatch(/^bubl-[a-z0-9]{9}$/);
    expect(newVisitorId).not.toBe('expired-visitor-id');

    // Verify conversation ID was also cleaned up
    const conversationId = await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      return localStorage.getItem(`bubl_conversation_${websiteId}`);
    });
    expect(conversationId).toBeNull();
  });

  test('should handle localStorage unavailability gracefully', async ({ page }) => {
    // Mock localStorage to throw errors
    await page.addInitScript(() => {
      const originalSetItem = localStorage.setItem;
      const originalGetItem = localStorage.getItem;
      
      localStorage.setItem = () => {
        throw new Error('localStorage unavailable');
      };
      
      localStorage.getItem = () => {
        throw new Error('localStorage unavailable');
      };
      
      window.testWebsiteId = 'test-website-789';
    });

    // Test fallback behavior
    const fallbackVisitorId = await page.evaluate(() => {
      function generateId() {
        return 'bubl-' + Math.random().toString(36).substr(2, 9);
      }
      
      function getStoredVisitorId() {
        try {
          const websiteId = (window as any).testWebsiteId;
          const key = `bubl_visitor_${websiteId}`;
          let visitorId = localStorage.getItem(key);
          if (!visitorId) {
            visitorId = generateId();
            localStorage.setItem(key, visitorId);
          }
          return visitorId;
        } catch (e) {
          return generateId();
        }
      }
      
      return getStoredVisitorId();
    });

    expect(fallbackVisitorId).toMatch(/^bubl-[a-z0-9]{9}$/);
  });

  test('should store and retrieve conversation ID', async ({ page }) => {
    await page.addInitScript(() => {
      window.testWebsiteId = 'test-website-conv';
    });

    const testConversationId = 'test-conversation-uuid-123';

    // Store conversation ID
    await page.evaluate((conversationId) => {
      const websiteId = (window as any).testWebsiteId;
      
      function setStoredConversationId(id: string) {
        try {
          const key = `bubl_conversation_${websiteId}`;
          localStorage.setItem(key, id);
        } catch (e) {
          // Silent fail
        }
      }
      
      setStoredConversationId(conversationId);
    }, testConversationId);

    // Retrieve conversation ID
    const retrievedId = await page.evaluate(() => {
      const websiteId = (window as any).testWebsiteId;
      
      function getStoredConversationId() {
        try {
          const key = `bubl_conversation_${websiteId}`;
          return localStorage.getItem(key);
        } catch (e) {
          return null;
        }
      }
      
      return getStoredConversationId();
    });

    expect(retrievedId).toBe(testConversationId);
  });
});

test.describe('Chat History API', () => {
  test('should return empty history for new visitor', async ({ request }) => {
    const websiteId = 'test-website-id';
    const visitorId = 'new-visitor-id';

    const response = await request.get(
      `/api/chat/history/visitor/${websiteId}/${visitorId}`
    );

    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.conversation).toBeNull();
    expect(data.messages).toEqual([]);
  });

  test('should handle invalid parameters gracefully', async ({ request }) => {
    // Test with missing parameters
    const response1 = await request.get('/api/chat/history/visitor//');
    expect(response1.status()).toBe(400);

    // Test with malformed websiteId
    const response2 = await request.get('/api/chat/history/visitor/invalid/visitor-id');
    expect(response2.ok()).toBeTruthy(); // Should not fail, just return empty
  });

  test('should include proper CORS headers', async ({ request }) => {
    const response = await request.get('/api/chat/history/visitor/test/test');
    
    expect(response.headers()['access-control-allow-origin']).toBe('*');
    expect(response.headers()['access-control-allow-methods']).toContain('GET');
    expect(response.headers()['access-control-allow-headers']).toContain('Content-Type');
  });
});
